<?php
// api/backup_process_zip.php - Sistema di backup con creazione ZIP garantita
// Percorso: api/backup_process_zip.php

// Avvia sessione solo se non è già attiva
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/db_config.php';
require_once __DIR__ . '/../includes/logger.php';

// Verifica autenticazione admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Accesso negato']);
    exit;
}

$logger = Logger::getInstance();

try {
    // Inizializza il processo di backup
    $logger->info("Inizio processo di backup ZIP da utente: " . $_SESSION['username']);
    
    // Crea directory backups se non esiste
    $backupsDir = '../backups';
    if (!file_exists($backupsDir)) {
        if (!mkdir($backupsDir, 0755, true)) {
            throw new Exception('Impossibile creare la directory backups');
        }
        $logger->info("Directory backups creata: $backupsDir");
    }
    
    // Verifica permessi scrittura
    if (!is_writable($backupsDir)) {
        throw new Exception('Directory backups non scrivibile');
    }
    
    // Nome file backup con timestamp
    $timestamp = date('Y-m-d_H-i-s');
    $backupFilename = "backup_asdp_{$timestamp}.zip";
    $backupPath = $backupsDir . '/' . $backupFilename;
    
    // Crea directory temporanea per il backup
    $tempDir = $backupsDir . '/temp_' . $timestamp;
    if (!mkdir($tempDir, 0755, true)) {
        throw new Exception('Impossibile creare directory temporanea');
    }
    
    $logger->info("Directory temporanea creata: $tempDir");
    
    // 1. BACKUP DATABASE
    $logger->info("Inizio backup database");
    $dbBackupContent = createDatabaseBackup();
    
    // Crea directory database nella temp
    $dbDir = $tempDir . '/database';
    mkdir($dbDir, 0755, true);
    
    // Salva backup database
    $dbFile = $dbDir . '/asdp_database.sql';
    if (file_put_contents($dbFile, $dbBackupContent) === false) {
        throw new Exception('Impossibile salvare il backup del database');
    }
    $logger->info("Backup database completato: $dbFile");
    
    // 2. BACKUP FILES del progetto
    $logger->info("Inizio backup files");
    $excludeDirs = ['backups', 'logs', 'cache', 'vendor', '.git', 'node_modules', 'tmp'];
    $excludeFiles = ['.env', '.env.local', 'composer.lock', '*.tmp', '*.log', '*.cache'];
    
    $filesAdded = copyDirectoryToBackup('..', $tempDir . '/files', $excludeDirs, $excludeFiles);
    $logger->info("Backup files completato. Files copiati: $filesAdded");
    
    // 3. AGGIUNGI REPORT DI BACKUP
    $backupReport = generateBackupReport($timestamp, $filesAdded);
    $reportFile = $tempDir . '/backup_info.txt';
    file_put_contents($reportFile, $backupReport);
    
    // 4. CREA ZIP usando metodi alternativi
    $zipCreated = false;
    $zipMethod = '';
    
    // Metodo 1: Prova ZipArchive se disponibile
    if (class_exists('ZipArchive')) {
        $logger->info("Tentativo creazione ZIP con ZipArchive");
        $zipCreated = createZipWithZipArchive($tempDir, $backupPath);
        if ($zipCreated) {
            $zipMethod = 'ZipArchive';
            $logger->info("ZIP creato con ZipArchive");
        }
    }
    
    // Metodo 2: Usa PowerShell Compress-Archive se ZipArchive non funziona
    if (!$zipCreated) {
        $logger->info("Tentativo creazione ZIP con PowerShell");
        $zipCreated = createZipWithPowerShell($tempDir, $backupPath);
        if ($zipCreated) {
            $zipMethod = 'PowerShell';
            $logger->info("ZIP creato con PowerShell");
        }
    }
    
    // Metodo 3: Usa 7-Zip se disponibile
    if (!$zipCreated) {
        $logger->info("Tentativo creazione ZIP con 7-Zip");
        $zipCreated = createZipWith7Zip($tempDir, $backupPath);
        if ($zipCreated) {
            $zipMethod = '7-Zip';
            $logger->info("ZIP creato con 7-Zip");
        }
    }
    
    // Se nessun metodo funziona, mantieni la directory
    if (!$zipCreated) {
        $logger->info("Impossibile creare ZIP, mantengo backup in directory");
        $finalBackupPath = $backupsDir . '/backup_asdp_' . $timestamp;
        rename($tempDir, $finalBackupPath);
        $backupFilename = 'backup_asdp_' . $timestamp;
        $backupPath = $finalBackupPath;
        $zipMethod = 'Directory';
    } else {
        // Rimuovi directory temporanea se ZIP creato con successo
        removeDirectory($tempDir);
        $logger->info("Directory temporanea rimossa");
    }
    
    // Verifica che il backup sia stato creato
    if (!file_exists($backupPath)) {
        throw new Exception('Backup non creato correttamente');
    }
    
    // Calcola dimensione
    $fileSize = is_file($backupPath) ? filesize($backupPath) : calculateDirectorySize($backupPath);
    
    $logger->info("Backup completato con metodo: $zipMethod. Dimensione: " . formatBytes($fileSize));
    
    // 5. SALVA INFORMAZIONI NEL DATABASE
    $pdo = getConnection();
    
    // Crea tabella backups se non esiste
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS backups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            created_at DATETIME NOT NULL,
            size BIGINT NOT NULL,
            UNIQUE KEY filename (filename)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    $pdo->exec($createTableSQL);
    
    // Inserisci record del backup
    $stmt = $pdo->prepare("
        INSERT INTO backups (filename, created_at, size) 
        VALUES (?, NOW(), ?)
    ");
    $stmt->execute([$backupFilename, $fileSize]);
    
    $logger->info("Record backup salvato nel database");
    
    // Risposta di successo
    echo json_encode([
        'success' => true,
        'message' => 'Backup completato con successo',
        'filename' => $backupFilename,
        'size' => formatBytes($fileSize),
        'method' => $zipMethod,
        'files_count' => $filesAdded
    ]);
    
} catch (Exception $e) {
    $logger->error("Errore durante il backup: " . $e->getMessage());
    
    // Pulisci directory temporanea in caso di errore
    if (isset($tempDir) && file_exists($tempDir)) {
        removeDirectory($tempDir);
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore durante il backup: ' . $e->getMessage()
    ]);
}

/**
 * Crea ZIP usando ZipArchive
 */
function createZipWithZipArchive($sourceDir, $zipPath) {
    try {
        $zip = new ZipArchive();
        $result = $zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);
        
        if ($result !== TRUE) {
            return false;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($sourceDir),
            RecursiveIteratorIterator::LEAVES_ONLY
        );
        
        foreach ($iterator as $file) {
            if (!$file->isDir()) {
                $filePath = $file->getRealPath();
                $relativePath = substr($filePath, strlen($sourceDir) + 1);
                
                // Fix: Rimuovi il nome della directory temporanea dal percorso
                // Se il percorso inizia con 'database/' o 'files/' o 'backup_info.txt', mantienilo così
                // Altrimenti, cerca di estrarre solo la parte dopo la prima directory
                $pathParts = explode(DIRECTORY_SEPARATOR, $relativePath);
                if (count($pathParts) > 1 && !in_array($pathParts[0], ['database', 'files'])) {
                    // Rimuovi la prima parte del percorso (nome directory temporanea)
                    array_shift($pathParts);
                    $relativePath = implode('/', $pathParts);
                } else {
                    // Normalizza i separatori per ZIP (sempre forward slash)
                    $relativePath = str_replace(DIRECTORY_SEPARATOR, '/', $relativePath);
                }
                
                $zip->addFile($filePath, $relativePath);
            }
        }
        
        $zip->close();
        return file_exists($zipPath) && filesize($zipPath) > 0;
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Crea ZIP usando PowerShell Compress-Archive
 */
function createZipWithPowerShell($sourceDir, $zipPath) {
    try {
        $sourceDir = realpath($sourceDir);
        $zipPath = realpath(dirname($zipPath)) . '\\' . basename($zipPath);
        
        // Fix: Crea ZIP del contenuto della directory, non della directory stessa
        // Questo evita la nidificazione della directory temporanea
        $command = "powershell -Command \"Compress-Archive -Path '$sourceDir\\*' -DestinationPath '$zipPath' -Force\"";
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        return $returnCode === 0 && file_exists($zipPath) && filesize($zipPath) > 0;
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Crea ZIP usando 7-Zip se disponibile
 */
function createZipWith7Zip($sourceDir, $zipPath) {
    try {
        // Percorsi comuni per 7-Zip
        $sevenZipPaths = [
            'C:\\Program Files\\7-Zip\\7z.exe',
            'C:\\Program Files (x86)\\7-Zip\\7z.exe'
        ];
        
        $sevenZipPath = null;
        foreach ($sevenZipPaths as $path) {
            if (file_exists($path)) {
                $sevenZipPath = $path;
                break;
            }
        }
        
        if (!$sevenZipPath) {
            return false;
        }
        
        // Fix: Comprimi il contenuto della directory, non la directory stessa
        // Questo evita la nidificazione della directory temporanea
        $command = "\"$sevenZipPath\" a \"$zipPath\" \"$sourceDir\\*\"";
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        return $returnCode === 0 && file_exists($zipPath) && filesize($zipPath) > 0;
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Crea backup del database usando PDO
 */
function createDatabaseBackup() {
    $pdo = getConnection();
    $backup = "-- ASDP Database Backup\n";
    $backup .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
    $backup .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
    
    // Ottieni lista tabelle
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        // Struttura tabella
        $createTable = $pdo->query("SHOW CREATE TABLE `$table`")->fetch();
        $backup .= "-- Structure for table `$table`\n";
        $backup .= "DROP TABLE IF EXISTS `$table`;\n";
        $backup .= $createTable['Create Table'] . ";\n\n";
        
        // Dati tabella
        $rows = $pdo->query("SELECT * FROM `$table`")->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($rows)) {
            $backup .= "-- Data for table `$table`\n";
            
            foreach ($rows as $row) {
                $values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $values[] = 'NULL';
                    } else {
                        $values[] = $pdo->quote($value);
                    }
                }
                $backup .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
            }
            $backup .= "\n";
        }
    }
    
    $backup .= "SET FOREIGN_KEY_CHECKS=1;\n";
    return $backup;
}

/**
 * Copia directory ricorsivamente con esclusioni
 */
function copyDirectoryToBackup($source, $destination, $excludeDirs = [], $excludeFiles = []) {
    $filesAdded = 0;
    
    if (!file_exists($destination)) {
        mkdir($destination, 0755, true);
    }
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($iterator as $item) {
        $relativePath = substr($item->getPathname(), strlen($source) + 1);
        $pathParts = explode(DIRECTORY_SEPARATOR, $relativePath);
        
        // Controlla esclusioni directory
        $skip = false;
        foreach ($excludeDirs as $excludeDir) {
            if (in_array($excludeDir, $pathParts)) {
                $skip = true;
                break;
            }
        }
        
        if ($skip) continue;
        
        // Controlla esclusioni file
        if ($item->isFile()) {
            $filename = $item->getFilename();
            foreach ($excludeFiles as $excludePattern) {
                if (fnmatch($excludePattern, $filename)) {
                    $skip = true;
                    break;
                }
            }
            if ($skip) continue;
        }
        
        $targetPath = $destination . DIRECTORY_SEPARATOR . $relativePath;
        
        if ($item->isDir()) {
            if (!file_exists($targetPath)) {
                mkdir($targetPath, 0755, true);
            }
        } else {
            $targetDir = dirname($targetPath);
            if (!file_exists($targetDir)) {
                mkdir($targetDir, 0755, true);
            }
            copy($item->getPathname(), $targetPath);
            $filesAdded++;
        }
    }
    
    return $filesAdded;
}

/**
 * Genera report di backup
 */
function generateBackupReport($timestamp, $filesCount) {
    $report = "ASDP - BACKUP REPORT\n";
    $report .= "==========================================\n\n";
    $report .= "Data/Ora: " . date('d/m/Y H:i:s') . "\n";
    $report .= "Utente: " . $_SESSION['username'] . "\n";
    $report .= "Versione PHP: " . PHP_VERSION . "\n";
    $report .= "Sistema: " . PHP_OS . "\n\n";
    
    $report .= "CONTENUTO BACKUP:\n";
    $report .= "- Database completo (struttura + dati) in /database/\n";
    $report .= "- File applicazione ($filesCount files) in /files/\n";
    $report .= "- Configurazioni\n";
    $report .= "- Documentazione\n\n";
    
    $report .= "DIRECTORY ESCLUSE:\n";
    $report .= "- /backups (backup precedenti)\n";
    $report .= "- /logs (file di log)\n";
    $report .= "- /cache (file temporanei)\n";
    $report .= "- /vendor (dipendenze)\n";
    $report .= "- /.git (controllo versione)\n\n";
    
    $report .= "FILE ESCLUSI:\n";
    $report .= "- .env (configurazioni sensibili)\n";
    $report .= "- *.tmp, *.log, *.cache\n\n";
    
    $report .= "ISTRUZIONI RIPRISTINO:\n";
    $report .= "1. Estrarre il contenuto dell'archivio\n";
    $report .= "2. Copiare /files/ nella directory web\n";
    $report .= "3. Importare /database/asdp_database.sql in MySQL\n";
    $report .= "4. Configurare file .env con credenziali corrette\n";
    $report .= "5. Verificare permessi directory\n";
    
    return $report;
}

/**
 * Rimuove directory ricorsivamente
 */
function removeDirectory($dir) {
    if (!file_exists($dir)) return;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    
    foreach ($iterator as $file) {
        if ($file->isDir()) {
            rmdir($file->getPathname());
        } else {
            unlink($file->getPathname());
        }
    }
    
    rmdir($dir);
}

/**
 * Calcola dimensione directory
 */
function calculateDirectorySize($directory) {
    $size = 0;
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $size += $file->getSize();
        }
    }
    
    return $size;
}

/**
 * Formatta bytes in formato leggibile
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?>