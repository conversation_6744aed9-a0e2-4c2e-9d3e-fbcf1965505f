/**
 * damper_styles.css - Stili specifici per la sezione dissipatori sismici
 * Path: /inertial_mass/assets/css/damper_styles.css
 * 
 * Stili per la visualizzazione delle raccomandazioni dei dissipatori sismici
 * nel modulo massa inerziale ASDP v2.4.2
 */

/* Sezione principale dissipatori */
.results-dampers {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
    border: 1px solid #4a5568 !important;
    border-radius: 12px !important;
    padding: 2rem !important;
    margin: 2rem 0 !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

.results-dampers h4 {
    color: #f7fafc !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    margin-bottom: 1.5rem !important;
    text-align: center !important;
    border-bottom: 2px solid #4299e1 !important;
    padding-bottom: 0.5rem !important;
}

/* Griglia riepilogo dissipatori */
.damper-summary-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
    margin-bottom: 2rem !important;
}

.damper-summary-item {
    background: rgba(66, 153, 225, 0.1) !important;
    border: 1px solid rgba(66, 153, 225, 0.3) !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
}

.damper-summary-item:hover {
    background: rgba(66, 153, 225, 0.2) !important;
    border-color: rgba(66, 153, 225, 0.5) !important;
    transform: translateY(-2px) !important;
}

.damper-label {
    display: block !important;
    color: #a0aec0 !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    margin-bottom: 0.5rem !important;
}

.damper-value {
    display: block !important;
    color: #4299e1 !important;
    font-size: 1.2rem !important;
    font-weight: 700 !important;
}

/* Tabella raccomandazioni dissipatori */
.dampers-table {
    width: 100% !important;
    border-collapse: collapse !important;
    background: rgba(45, 55, 72, 0.6) !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    margin: 1rem 0 !important;
}

.dampers-table thead {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
}

.dampers-table th {
    color: #f7fafc !important;
    font-weight: 600 !important;
    padding: 1rem 0.75rem !important;
    text-align: center !important;
    font-size: 0.9rem !important;
    border-bottom: 2px solid #4299e1 !important;
}

.dampers-table td {
    color: #e2e8f0 !important;
    padding: 0.875rem 0.75rem !important;
    text-align: center !important;
    border-bottom: 1px solid rgba(74, 85, 104, 0.3) !important;
    font-size: 0.9rem !important;
}

.dampers-table tbody tr:hover {
    background: rgba(66, 153, 225, 0.1) !important;
}

.dampers-total-row {
    background: rgba(72, 187, 120, 0.2) !important;
    border-top: 2px solid #48bb78 !important;
}

.dampers-total-row td {
    color: #48bb78 !important;
    font-weight: 700 !important;
    font-size: 1rem !important;
}

/* Sezione analisi tecnica */
.dampers-technical {
    background: rgba(45, 55, 72, 0.4) !important;
    border: 1px solid rgba(74, 85, 104, 0.5) !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
}

.dampers-technical h5 {
    color: #f7fafc !important;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
    border-bottom: 1px solid #4a5568 !important;
    padding-bottom: 0.5rem !important;
}

.technical-content {
    color: #cbd5e0 !important;
    line-height: 1.6 !important;
    font-size: 0.95rem !important;
    white-space: pre-line !important;
}

/* Sezione note specifiche edificio */
.dampers-building-notes {
    background: rgba(128, 90, 213, 0.1) !important;
    border: 1px solid rgba(128, 90, 213, 0.3) !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
}

.dampers-building-notes h5 {
    color: #f7fafc !important;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
    border-bottom: 1px solid #805ad5 !important;
    padding-bottom: 0.5rem !important;
}

.building-notes-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
}

.building-note {
    display: flex !important;
    align-items: flex-start !important;
    gap: 0.75rem !important;
    padding: 0.75rem !important;
    background: rgba(128, 90, 213, 0.1) !important;
    border-radius: 6px !important;
    border-left: 3px solid #805ad5 !important;
}

.note-icon {
    font-size: 1.1rem !important;
    flex-shrink: 0 !important;
}

.note-text {
    color: #e2e8f0 !important;
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .damper-summary-grid {
        grid-template-columns: 1fr !important;
    }
    
    .dampers-table {
        font-size: 0.8rem !important;
    }
    
    .dampers-table th,
    .dampers-table td {
        padding: 0.5rem 0.25rem !important;
    }
    
    .results-dampers {
        padding: 1rem !important;
        margin: 1rem 0 !important;
    }
    
    .building-note {
        flex-direction: column !important;
        gap: 0.5rem !important;
    }
}

/* Animazioni specifiche */
@keyframes damperSlideIn {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.dampers-table tr[data-animate-damper] {
    animation: damperSlideIn 0.4s ease forwards;
}

.dampers-total-row {
    animation: damperSlideIn 0.4s ease forwards;
    animation-delay: 0.2s;
}

/* Effetti hover migliorati */
.damper-summary-item:hover .damper-value {
    color: #63b3ed !important;
    transform: scale(1.05) !important;
}

.building-note:hover {
    background: rgba(128, 90, 213, 0.2) !important;
    transform: translateX(5px) !important;
    transition: all 0.3s ease !important;
}

/* Compatibilità con modal 1400px */
@media (min-width: 1200px) {
    .damper-summary-grid {
        grid-template-columns: repeat(4, 1fr) !important;
    }
    
    .dampers-table {
        font-size: 1rem !important;
    }
    
    .results-dampers {
        padding: 2.5rem !important;
    }
}
