<?php
// tools/final_test.php - Test finale per verificare il backup completo
// Percorso: tools/final_test.php

// Configura sessione admin PRIMA di qualsiasi output
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_admin';
$_SESSION['role'] = 'admin';

// Simula richiesta POST
$_POST['action'] = 'backup';

echo "=== TEST FINALE BACKUP ASDP ===\n";
echo "Data: " . date('Y-m-d H:i:s') . "\n\n";
echo "✓ Sessione admin configurata\n";
echo "✓ Richiesta POST simulata\n";

try {
    // Avvia output buffering per catturare l'output del backup
    ob_start();
    include __DIR__ . '/../api/backup_process_zip.php';
    $output = ob_get_clean();
    
    // Rimuove eventuali caratteri di controllo o output duplicato
    $output = trim($output);
    
    // Se l'output contiene più JSON, prende solo l'ultimo
    $lines = explode("\n", $output);
    $jsonLine = '';
    foreach (array_reverse($lines) as $line) {
        $line = trim($line);
        if (!empty($line) && (strpos($line, '{') === 0 || strpos($line, '[') === 0)) {
            $jsonLine = $line;
            break;
        }
    }
    
    if (empty($jsonLine)) {
        $jsonLine = $output;
    }
    
    echo "✓ Script di backup eseguito\n";
    
    // Decodifica risposta JSON
    $result = json_decode($jsonLine, true);
    
    if (!$result) {
        echo "❌ ERRORE: Output non è JSON valido\n";
        echo "Output completo ricevuto: " . substr($output, 0, 200) . "...\n";
        echo "JSON estratto: " . substr($jsonLine, 0, 200) . "...\n";
        exit(1);
    }
    
    if (!$result['success']) {
        echo "❌ ERRORE: Backup fallito\n";
        echo "Messaggio: " . $result['message'] . "\n";
        exit(1);
    }
    
    echo "✅ BACKUP COMPLETATO CON SUCCESSO!\n\n";
    echo "Dettagli backup:\n";
    echo "- File: " . $result['filename'] . "\n";
    echo "- Dimensione: " . $result['size'] . "\n";
    echo "- Metodo: " . $result['method'] . "\n";
    echo "- File copiati: " . $result['files_count'] . "\n\n";
    
    // Verifica struttura ZIP
    $backupPath = __DIR__ . '/../backups/' . $result['filename'];
    
    if (!file_exists($backupPath)) {
        echo "❌ ERRORE: File backup non trovato: {$backupPath}\n";
        exit(1);
    }
    
    echo "✓ File backup trovato\n";
    
    if (class_exists('ZipArchive')) {
        $zip = new ZipArchive();
        if ($zip->open($backupPath) === TRUE) {
            echo "✓ ZIP aperto per analisi\n\n";
            
            echo "=== ANALISI STRUTTURA ZIP ===\n";
            
            $hasNestedFolders = false;
            $hasDatabase = false;
            $hasFiles = false;
            $hasBackupInfo = false;
            $structure = [];
            
            for ($i = 0; $i < $zip->numFiles; $i++) {
                $filename = $zip->getNameIndex($i);
                $structure[] = $filename;
                
                // Controlla cartelle nidificate indesiderate
                if (preg_match('/^temp_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\//i', $filename)) {
                    $hasNestedFolders = true;
                }
                
                // Verifica struttura corretta
                if (strpos($filename, 'database/') === 0) $hasDatabase = true;
                if (strpos($filename, 'files/') === 0) $hasFiles = true;
                if ($filename === 'backup_info.txt') $hasBackupInfo = true;
            }
            
            $zip->close();
            
            // Mostra primi 10 file
            echo "Primi 10 file nel ZIP:\n";
            for ($i = 0; $i < min(10, count($structure)); $i++) {
                echo "- " . $structure[$i] . "\n";
            }
            if (count($structure) > 10) {
                echo "... e altri " . (count($structure) - 10) . " file\n";
            }
            echo "\n";
            
            // Risultati verifica
            echo "=== RISULTATI VERIFICA ===\n";
            
            if ($hasNestedFolders) {
                echo "❌ PROBLEMA: Cartelle nidificate rilevate!\n";
                echo "   La struttura contiene ancora directory temporanee\n";
                exit(1);
            } else {
                echo "✅ STRUTTURA CORRETTA: Nessuna cartella nidificata\n";
            }
            
            echo "\nContenuto verificato:\n";
            echo "- Cartella database/: " . ($hasDatabase ? '✅' : '❌') . "\n";
            echo "- Cartella files/: " . ($hasFiles ? '✅' : '❌') . "\n";
            echo "- File backup_info.txt: " . ($hasBackupInfo ? '✅' : '❌') . "\n";
            
            if ($hasDatabase && $hasFiles && $hasBackupInfo) {
                echo "\n🎉 TUTTI I TEST SUPERATI!\n";
                echo "La struttura del backup ZIP è corretta e completa.\n";
                exit(0);
            } else {
                echo "\n❌ ALCUNI CONTENUTI MANCANTI\n";
                exit(1);
            }
            
        } else {
            echo "❌ ERRORE: Impossibile aprire ZIP per analisi\n";
            exit(1);
        }
    } else {
        echo "⚠️  ZipArchive non disponibile per analisi dettagliata\n";
        echo "✅ Backup creato, ma impossibile verificare struttura\n";
        exit(0);
    }
    
} catch (Exception $e) {
    echo "❌ ERRORE DURANTE IL TEST: " . $e->getMessage() . "\n";
    exit(1);
}
?>