<?php
class Logger {
    private static $logFile = 'app.log';
    private static $logPath;
    private static $instance = null;

    private function __construct() {
        self::$logPath = __DIR__ . '/../logs/' . self::$logFile;
        
        // Crea la directory logs se non esiste
        if (!is_dir(__DIR__ . '/../logs')) {
            mkdir(__DIR__ . '/../logs', 0777, true);
        }
        
        // Crea il file di log se non esiste
        if (!file_exists(self::$logPath)) {
            touch(self::$logPath);
            chmod(self::$logPath, 0666);
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function log($type, $message, $data = null) {
        $timestamp = date('Y-m-d H:i:s');
        $sessionId = session_id() ?: 'no-session';
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'no-user';
        $userRole = isset($_SESSION['role']) ? $_SESSION['role'] : 'no-role';
        
        // Gestisce il caso CLI dove REMOTE_ADDR non è disponibile
        $remoteAddr = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'CLI';
        
        $logEntry = sprintf(
            "[%s] [%s] [Session: %s] [User: %s] [Role: %s] [%s] %s",
            $timestamp,
            strtoupper($type),
            $sessionId,
            $userId,
            $userRole,
            $remoteAddr,
            $message
        );

        if ($data !== null) {
            $logEntry .= "\nData: " . print_r($data, true);
        }

        $logEntry .= "\n" . str_repeat('-', 100) . "\n";

        error_log($logEntry . PHP_EOL, 3, self::$logPath);
    }

    public function debug($message, $data = null) {
        $this->log('DEBUG', $message, $data);
    }

    public function info($message, $data = null) {
        $this->log('INFO', $message, $data);
    }

    public function warning($message, $data = null) {
        $this->log('WARNING', $message, $data);
    }

    public function error($message, $data = null) {
        $this->log('ERROR', $message, $data);
    }

    public function critical($message, $data = null) {
        $this->log('CRITICAL', $message, $data);
    }

    public function logRequest() {
        $requestData = [
            'METHOD' => $_SERVER['REQUEST_METHOD'],
            'URI' => $_SERVER['REQUEST_URI'],
            'QUERY' => $_SERVER['QUERY_STRING'] ?? '',
            'HEADERS' => getallheaders(),
            'GET' => $_GET,
            'POST' => $_POST,
            'SESSION' => $_SESSION ?? [],
            'COOKIES' => $_COOKIE
        ];

        $this->info('HTTP Request', $requestData);
    }

    public function logDatabase($query, $params = [], $error = null) {
        $data = [
            'query' => $query,
            'parameters' => $params
        ];

        if ($error) {
            $data['error'] = $error;
            $this->error('Database Error', $data);
        } else {
            $this->debug('Database Query', $data);
        }
    }

    public function logSession() {
        $sessionData = [
            'id' => session_id(),
            'status' => session_status(),
            'data' => $_SESSION ?? []
        ];

        $this->debug('Session State', $sessionData);
    }

    public function logAuthentication($username, $success, $error = null) {
        $data = [
            'username' => $username,
            'success' => $success
        ];

        if ($error) {
            $data['error'] = $error;
            $this->warning('Authentication Failed', $data);
        } else {
            $this->info('Authentication Success', $data);
        }
    }

    public function getLogPath() {
        return self::$logPath;
    }
}