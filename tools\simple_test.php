<?php
// tools/simple_test.php - Test semplificato per debug
// Percorso: tools/simple_test.php

echo "Test iniziato...\n";
file_put_contents(__DIR__ . '/debug.log', "Test iniziato: " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);

// Avvia sessione per simulare utente admin
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_admin';
$_SESSION['role'] = 'admin';

// Verifica che la sessione sia configurata correttamente
echo "User ID: " . ($_SESSION['user_id'] ?? 'NON IMPOSTATO') . "\n";
echo "Role: " . ($_SESSION['role'] ?? 'NON IMPOSTATO') . "\n";
file_put_contents(__DIR__ . '/debug.log', "User ID: " . ($_SESSION['user_id'] ?? 'NON IMPOSTATO') . "\n", FILE_APPEND);
file_put_contents(__DIR__ . '/debug.log', "Role: " . ($_SESSION['role'] ?? 'NON IMPOSTATO') . "\n", FILE_APPEND);

echo "Sessione configurata...\n";
file_put_contents(__DIR__ . '/debug.log', "Sessione configurata\n", FILE_APPEND);

try {
    require_once __DIR__ . '/../includes/db_config.php';
    echo "Database config caricato...\n";
    file_put_contents(__DIR__ . '/debug.log', "Database config OK\n", FILE_APPEND);
    
    require_once __DIR__ . '/../includes/logger.php';
    echo "Logger caricato...\n";
    file_put_contents(__DIR__ . '/debug.log', "Logger OK\n", FILE_APPEND);
    
    // Simula richiesta POST
    $_POST['action'] = 'backup';
    
    echo "Tentativo di includere backup_process_zip.php...\n";
    file_put_contents(__DIR__ . '/debug.log', "Tentativo include backup\n", FILE_APPEND);
    
    ob_start();
    include __DIR__ . '/../api/backup_process_zip.php';
    $output = ob_get_clean();
    
    echo "Backup completato. Output: " . substr($output, 0, 200) . "...\n";
    file_put_contents(__DIR__ . '/debug.log', "Backup output: " . $output . "\n", FILE_APPEND);
    
} catch (Exception $e) {
    echo "Errore: " . $e->getMessage() . "\n";
    file_put_contents(__DIR__ . '/debug.log', "Errore: " . $e->getMessage() . "\n", FILE_APPEND);
}

echo "Test completato.\n";
file_put_contents(__DIR__ . '/debug.log', "Test completato\n", FILE_APPEND);
?>