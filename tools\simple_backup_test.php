<?php
// tools/simple_backup_test.php - Test backup semplificato
// Percorso: tools/simple_backup_test.php

echo "Inizio test backup...\n";

// Configura sessione admin
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_admin';
$_SESSION['role'] = 'admin';

echo "Sessione configurata\n";

// Simula richiesta POST
$_POST['action'] = 'backup';

echo "POST configurato\n";
echo "Eseguendo backup...\n";

// Include direttamente il backup
include __DIR__ . '/../api/backup_process_zip.php';

echo "\nBackup completato\n";
?>