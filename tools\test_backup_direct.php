<?php
// tools/test_backup_direct.php - Test diretto del backup senza controlli di autenticazione
// Percorso: tools/test_backup_direct.php

echo "Test backup diretto iniziato...\n";

// Avvia sessione
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_admin';
$_SESSION['role'] = 'admin';

require_once __DIR__ . '/../includes/db_config.php';
require_once __DIR__ . '/../includes/logger.php';

$logger = Logger::getInstance();

try {
    echo "Inizio processo di backup ZIP...\n";
    $logger->info("Inizio processo di backup ZIP da test diretto");
    
    // Crea directory backups se non esiste
    $backupsDir = __DIR__ . '/../backups';
    if (!file_exists($backupsDir)) {
        if (!mkdir($backupsDir, 0755, true)) {
            throw new Exception('Impossibile creare la directory backups');
        }
    }
    
    // Genera nome file backup
    $timestamp = date('Y-m-d_H-i-s');
    $backupFilename = "backup_asdp_{$timestamp}.zip";
    $backupPath = $backupsDir . '/' . $backupFilename;
    
    echo "Nome file backup: {$backupFilename}\n";
    
    // Crea directory temporanea
    $tempDir = sys_get_temp_dir() . '/temp_' . $timestamp;
    if (!mkdir($tempDir, 0755, true)) {
        throw new Exception('Impossibile creare directory temporanea');
    }
    
    echo "Directory temporanea: {$tempDir}\n";
    
    // Test creazione ZIP con ZipArchive
    if (class_exists('ZipArchive')) {
        echo "Tentativo creazione ZIP con ZipArchive...\n";
        
        $zip = new ZipArchive();
        $result = $zip->open($backupPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);
        
        if ($result === TRUE) {
            // Aggiungi un file di test
            $zip->addFromString('test.txt', 'File di test per verificare la struttura');
            $zip->addFromString('database/test_db.sql', 'Test database content');
            $zip->addFromString('files/test_file.txt', 'Test file content');
            $zip->addFromString('backup_info.txt', 'Test backup info');
            
            $zip->close();
            
            echo "ZIP creato con successo!\n";
            echo "Percorso: {$backupPath}\n";
            
            // Verifica struttura
            if (file_exists($backupPath)) {
                $zipCheck = new ZipArchive();
                if ($zipCheck->open($backupPath) === TRUE) {
                    echo "\nContenuto ZIP:\n";
                    for ($i = 0; $i < $zipCheck->numFiles; $i++) {
                        $filename = $zipCheck->getNameIndex($i);
                        echo "- {$filename}\n";
                    }
                    $zipCheck->close();
                    
                    echo "\n✅ Test completato con successo!\n";
                } else {
                    echo "❌ Errore: impossibile riaprire il ZIP per la verifica\n";
                }
            } else {
                echo "❌ Errore: file ZIP non trovato dopo la creazione\n";
            }
        } else {
            echo "❌ Errore nella creazione del ZIP: {$result}\n";
        }
    } else {
        echo "❌ ZipArchive non disponibile\n";
    }
    
    // Pulizia
    if (is_dir($tempDir)) {
        rmdir($tempDir);
    }
    
} catch (Exception $e) {
    echo "❌ Errore: " . $e->getMessage() . "\n";
}

echo "Test completato.\n";
?>