<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Flusso <PERSON> Reali - ASDP v2.5.0</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .button {
            background: #FF7043;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #FF8A65;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .error {
            margin: 10px 0;
            padding: 10px;
            background: #ffe8e8;
            border-left: 4px solid #f44336;
        }
        .warning {
            margin: 10px 0;
            padding: 10px;
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .info {
            margin: 10px 0;
            padding: 10px;
            background: #e8f4fd;
            border-left: 4px solid #2196f3;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background: #f2f2f2;
            font-weight: bold;
        }
        .highlight {
            background: #fff3cd;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .mock-asdp-interface {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .form-group {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .form-group label {
            min-width: 150px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            min-width: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Flusso Dati Reali - Modulo Massa Inerziale v2.5.0</h1>
        <p><strong>Data Test:</strong> <span id="testDate"></span></p>
        
        <div class="info">
            <h4>ℹ️ Obiettivo del Test</h4>
            <p>Verificare che il sistema di generazione report utilizzi dati reali provenienti dai calcoli effettivi invece di dati simulati o hardcoded.</p>
        </div>

        <!-- Simulazione Interfaccia ASDP -->
        <div class="test-section">
            <h3>🏗️ Simulazione Interfaccia ASDP</h3>
            <p>Simula i campi dell'interfaccia principale ASDP da cui vengono recuperati i dati reali.</p>
            
            <div class="mock-asdp-interface">
                <h4>Parametri Geografici</h4>
                <div class="form-group">
                    <label for="latitude">Latitudine:</label>
                    <input type="number" id="latitude" value="45.4642" step="0.0001">
                </div>
                <div class="form-group">
                    <label for="longitude">Longitudine:</label>
                    <input type="number" id="longitude" value="9.1900" step="0.0001">
                </div>
                
                <h4>Parametri Sismici</h4>
                <div class="form-group">
                    <label for="seismic-zone">Zona Sismica:</label>
                    <select id="seismic-zone">
                        <option value="1">Zona 1</option>
                        <option value="2" selected>Zona 2</option>
                        <option value="3">Zona 3</option>
                        <option value="4">Zona 4</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="slv-ag">ag (SLV):</label>
                    <input type="number" id="slv-ag" value="0.182" step="0.001" readonly>
                </div>
                <div class="form-group">
                    <label for="slv-f0">F0 (SLV):</label>
                    <input type="number" id="slv-f0" value="2.456" step="0.001" readonly>
                </div>
                <div class="form-group">
                    <label for="slv-tc">TC* (SLV):</label>
                    <input type="number" id="slv-tc" value="0.312" step="0.001" readonly>
                </div>
                <div class="form-group">
                    <label for="soil-category">Categoria Suolo:</label>
                    <select id="soil-category">
                        <option value="A">A - Roccia</option>
                        <option value="B" selected>B - Depositi granulari</option>
                        <option value="C">C - Depositi argillosi</option>
                        <option value="D">D - Depositi granulari sciolti</option>
                        <option value="E">E - Profili di terreno particolari</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="damping">Smorzamento (%):</label>
                    <input type="number" id="damping" value="5.0" step="0.1">
                </div>
                <div class="form-group">
                    <label for="q-factor">Fattore di Struttura (q):</label>
                    <input type="number" id="q-factor" value="3.9" step="0.1">
                </div>
            </div>
            
            <button class="button" onclick="updateSeismicParameters()">Aggiorna Parametri Sismici</button>
        </div>

        <!-- Test 1: Recupero Dati Sismici -->
        <div class="test-section">
            <h3>📊 Test 1: Recupero Dati Sismici Reali</h3>
            <p>Verifica che la funzione getCurrentSeismicData() recuperi i dati dall'interfaccia simulata.</p>
            <button class="button" onclick="testSeismicDataRetrieval()">Testa Recupero Dati Sismici</button>
            <div id="seismicDataResult"></div>
        </div>

        <!-- Test 2: Memorizzazione Dati Input -->
        <div class="test-section">
            <h3>💾 Test 2: Memorizzazione Dati Input</h3>
            <p>Simula la raccolta e memorizzazione dei dati di input come avviene nel calcolo reale.</p>
            <button class="button" onclick="testInputDataStorage()">Testa Memorizzazione Input</button>
            <div id="inputDataResult"></div>
        </div>

        <!-- Test 3: Popolamento Template Report -->
        <div class="test-section">
            <h3>📄 Test 3: Popolamento Template con Dati Reali</h3>
            <p>Verifica che il template del report venga popolato con i dati reali memorizzati.</p>
            <button class="button" onclick="testTemplatePopulation()">Testa Popolamento Template</button>
            <div id="templateResult"></div>
        </div>

        <!-- Test 4: Confronto Dati Simulati vs Reali -->
        <div class="test-section">
            <h3>🔍 Test 4: Confronto Dati Simulati vs Reali</h3>
            <p>Confronta i dati utilizzati nel report con quelli inseriti nell'interfaccia.</p>
            <button class="button" onclick="testDataComparison()">Confronta Dati</button>
            <div id="comparisonResult"></div>
        </div>

        <!-- Test 5: Verifica Assenza Dati Hardcoded -->
        <div class="test-section">
            <h3>🚫 Test 5: Verifica Assenza Dati Hardcoded</h3>
            <p>Controlla che non vengano utilizzati valori hardcoded o di test nel report finale.</p>
            <button class="button" onclick="testNoHardcodedData()">Verifica Dati Hardcoded</button>
            <div id="hardcodedResult"></div>
        </div>
    </div>

    <script>
        // Imposta la data del test
        document.getElementById('testDate').textContent = new Date().toLocaleString('it-IT');
        
        // Simula lo stato del modulo massa inerziale
        window.inertialMassState = {
            seismicData: null,
            lastInputData: null,
            lastCalculationResults: null,
            floors: []
        };

        // Simula la funzione getCurrentSeismicData (copiata dal file originale)
        function getCurrentSeismicData() {
            const coordinates = {
                lat: parseFloat(document.getElementById('latitude')?.value || 41.9028),
                lon: parseFloat(document.getElementById('longitude')?.value || 12.4964)
            };
            
            const soilCategorySelect = document.getElementById('soil-category');
            const selectedSoilCategory = soilCategorySelect?.value || 'C';
            
            let ag = parseFloat(document.getElementById('slv-ag')?.value || 0.062);
            let f0 = parseFloat(document.getElementById('slv-f0')?.value || 2.604);
            let tc = parseFloat(document.getElementById('slv-tc')?.value || 0.268);
            
            const zone = document.getElementById('seismic-zone')?.value || '3';
            
            const dampingInput = document.getElementById('damping');
            const damping = dampingInput ? parseFloat(dampingInput.value) : 5.0;
            
            const qFactorInput = document.getElementById('q-factor');
            const qFactor = qFactorInput ? parseFloat(qFactorInput.value) : 1.0;
            
            const seismicData = {
                lat: coordinates.lat,
                lon: coordinates.lon,
                zone: zone,
                ag: ag,
                F0: f0,
                TC: tc,
                soil_category: selectedSoilCategory,
                damping: damping,
                q_factor: qFactor
            };
            
            return seismicData;
        }

        // Aggiorna i parametri sismici in base alla zona selezionata
        function updateSeismicParameters() {
            const zone = document.getElementById('seismic-zone').value;
            const agInput = document.getElementById('slv-ag');
            const f0Input = document.getElementById('slv-f0');
            const tcInput = document.getElementById('slv-tc');
            
            // Valori realistici per zona sismica
            const zoneParams = {
                '1': { ag: 0.35, f0: 2.8, tc: 0.25 },
                '2': { ag: 0.25, f0: 2.6, tc: 0.28 },
                '3': { ag: 0.15, f0: 2.4, tc: 0.32 },
                '4': { ag: 0.05, f0: 2.2, tc: 0.35 }
            };
            
            const params = zoneParams[zone] || zoneParams['3'];
            agInput.value = params.ag;
            f0Input.value = params.f0;
            tcInput.value = params.tc;
            
            document.getElementById('seismicDataResult').innerHTML = `
                <div class="info">
                    <h4>✅ Parametri aggiornati per Zona ${zone}</h4>
                    <p>ag: ${params.ag}g, F0: ${params.f0}, TC*: ${params.tc}s</p>
                </div>
            `;
        }

        // Test 1: Recupero Dati Sismici
        function testSeismicDataRetrieval() {
            const resultDiv = document.getElementById('seismicDataResult');
            resultDiv.innerHTML = '<p>🔄 Testando recupero dati sismici...</p>';
            
            try {
                const seismicData = getCurrentSeismicData();
                window.inertialMassState.seismicData = seismicData;
                
                resultDiv.innerHTML = `
                    <div class="result">
                        <h4>✅ Dati sismici recuperati con successo!</h4>
                        <table class="data-table">
                            <tr><th>Parametro</th><th>Valore Recuperato</th><th>Fonte</th></tr>
                            <tr><td>Coordinate</td><td>${seismicData.lat}, ${seismicData.lon}</td><td>Campi latitude/longitude</td></tr>
                            <tr><td>Zona Sismica</td><td>${seismicData.zone}</td><td>Select seismic-zone</td></tr>
                            <tr><td>ag</td><td>${seismicData.ag} g</td><td>Input slv-ag</td></tr>
                            <tr><td>F0</td><td>${seismicData.F0}</td><td>Input slv-f0</td></tr>
                            <tr><td>TC*</td><td>${seismicData.TC} s</td><td>Input slv-tc</td></tr>
                            <tr><td>Categoria Suolo</td><td>${seismicData.soil_category}</td><td>Select soil-category</td></tr>
                            <tr><td>Smorzamento</td><td>${seismicData.damping}%</td><td>Input damping</td></tr>
                            <tr><td>Fattore q</td><td>${seismicData.q_factor}</td><td>Input q-factor</td></tr>
                        </table>
                        <p><strong>Verifica:</strong> Tutti i dati provengono da campi dell'interfaccia reale, non da valori hardcoded.</p>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Errore recupero dati sismici</h4>
                        <p><strong>Errore:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        // Test 2: Memorizzazione Dati Input
        function testInputDataStorage() {
            const resultDiv = document.getElementById('inputDataResult');
            resultDiv.innerHTML = '<p>🔄 Testando memorizzazione dati input...</p>';
            
            try {
                // Simula la raccolta dati come nel sistema reale
                const seismicData = window.inertialMassState.seismicData || getCurrentSeismicData();
                
                const inputData = {
                    location: {
                        lat: seismicData.lat,
                        lon: seismicData.lon
                    },
                    seismic_params: {
                        zone: seismicData.zone,
                        ag: seismicData.ag,
                        F0: seismicData.F0,
                        TC: seismicData.TC,
                        soil_category: seismicData.soil_category,
                        damping: seismicData.damping,
                        q_factor: seismicData.q_factor
                    },
                    building: {
                        construction_category: 'building',
                        structure_type: 'concrete',
                        slab_type: 'hollow_brick',
                        construction_year: 2015,
                        floors: [
                            { level: 1, area: 100, height: 3.5, use: 'residential' },
                            { level: 2, area: 100, height: 3.0, use: 'residential' }
                        ]
                    }
                };
                
                // Memorizza come nel sistema reale
                window.inertialMassState.lastInputData = { ...inputData };
                
                resultDiv.innerHTML = `
                    <div class="result">
                        <h4>✅ Dati input memorizzati correttamente!</h4>
                        <p><strong>Struttura dati memorizzata:</strong></p>
                        <ul>
                            <li><strong>location:</strong> lat=${inputData.location.lat}, lon=${inputData.location.lon}</li>
                            <li><strong>seismic_params:</strong> ${Object.keys(inputData.seismic_params).length} parametri</li>
                            <li><strong>building:</strong> ${Object.keys(inputData.building).length} caratteristiche</li>
                            <li><strong>floors:</strong> ${inputData.building.floors.length} piani</li>
                        </ul>
                        <p><strong>Verifica:</strong> I dati sono strutturati esattamente come nel sistema reale.</p>
                        <details>
                            <summary>Visualizza dati completi</summary>
                            <pre>${JSON.stringify(inputData, null, 2)}</pre>
                        </details>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Errore memorizzazione dati</h4>
                        <p><strong>Errore:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        // Test 3: Popolamento Template
        function testTemplatePopulation() {
            const resultDiv = document.getElementById('templateResult');
            resultDiv.innerHTML = '<p>🔄 Testando popolamento template...</p>';
            
            try {
                const inputData = window.inertialMassState.lastInputData;
                if (!inputData) {
                    throw new Error('Esegui prima il Test 2 per memorizzare i dati input');
                }
                
                // Simula il popolamento del template
                const templatePlaceholders = {
                    'COORDINATES': `${inputData.location.lat}, ${inputData.location.lon}`,
                    'SEISMIC_ZONE': inputData.seismic_params.zone,
                    'AG': inputData.seismic_params.ag,
                    'F0': inputData.seismic_params.F0,
                    'TC': inputData.seismic_params.TC,
                    'SOIL_CATEGORY': inputData.seismic_params.soil_category,
                    'CONSTRUCTION_CATEGORY': inputData.building.construction_category,
                    'STRUCTURE_TYPE': inputData.building.structure_type,
                    'SLAB_TYPE': inputData.building.slab_type,
                    'CONSTRUCTION_YEAR': inputData.building.construction_year
                };
                
                resultDiv.innerHTML = `
                    <div class="result">
                        <h4>✅ Template popolato con dati reali!</h4>
                        <table class="data-table">
                            <tr><th>Placeholder</th><th>Valore Sostituito</th><th>Origine Dato</th></tr>
                            <tr><td>{{COORDINATES}}</td><td>${templatePlaceholders.COORDINATES}</td><td>inputData.location</td></tr>
                            <tr><td>{{SEISMIC_ZONE}}</td><td>${templatePlaceholders.SEISMIC_ZONE}</td><td>inputData.seismic_params.zone</td></tr>
                            <tr><td>{{AG}}</td><td>${templatePlaceholders.AG}</td><td>inputData.seismic_params.ag</td></tr>
                            <tr><td>{{F0}}</td><td>${templatePlaceholders.F0}</td><td>inputData.seismic_params.F0</td></tr>
                            <tr><td>{{TC}}</td><td>${templatePlaceholders.TC}</td><td>inputData.seismic_params.TC</td></tr>
                            <tr><td>{{SOIL_CATEGORY}}</td><td>${templatePlaceholders.SOIL_CATEGORY}</td><td>inputData.seismic_params.soil_category</td></tr>
                        </table>
                        <p><strong>Verifica:</strong> Tutti i placeholder vengono sostituiti con dati reali dalla struttura memorizzata.</p>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Errore popolamento template</h4>
                        <p><strong>Errore:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        // Test 4: Confronto Dati
        function testDataComparison() {
            const resultDiv = document.getElementById('comparisonResult');
            resultDiv.innerHTML = '<p>🔄 Confrontando dati interfaccia vs memorizzati...</p>';
            
            try {
                const currentSeismic = getCurrentSeismicData();
                const storedData = window.inertialMassState.lastInputData;
                
                if (!storedData) {
                    throw new Error('Esegui prima il Test 2 per memorizzare i dati');
                }
                
                const comparisons = [
                    { field: 'Latitudine', current: currentSeismic.lat, stored: storedData.location.lat },
                    { field: 'Longitudine', current: currentSeismic.lon, stored: storedData.location.lon },
                    { field: 'Zona Sismica', current: currentSeismic.zone, stored: storedData.seismic_params.zone },
                    { field: 'ag', current: currentSeismic.ag, stored: storedData.seismic_params.ag },
                    { field: 'F0', current: currentSeismic.F0, stored: storedData.seismic_params.F0 },
                    { field: 'TC*', current: currentSeismic.TC, stored: storedData.seismic_params.TC }
                ];
                
                let allMatch = true;
                let tableRows = '';
                
                comparisons.forEach(comp => {
                    const match = comp.current === comp.stored;
                    allMatch = allMatch && match;
                    const status = match ? '✅' : '❌';
                    const rowClass = match ? '' : 'class="highlight"';
                    
                    tableRows += `
                        <tr ${rowClass}>
                            <td>${comp.field}</td>
                            <td>${comp.current}</td>
                            <td>${comp.stored}</td>
                            <td>${status}</td>
                        </tr>
                    `;
                });
                
                const resultClass = allMatch ? 'result' : 'warning';
                const resultIcon = allMatch ? '✅' : '⚠️';
                const resultMessage = allMatch ? 
                    'Tutti i dati corrispondono perfettamente!' : 
                    'Alcuni dati non corrispondono - verifica la sincronizzazione';
                
                resultDiv.innerHTML = `
                    <div class="${resultClass}">
                        <h4>${resultIcon} ${resultMessage}</h4>
                        <table class="data-table">
                            <tr><th>Campo</th><th>Valore Interfaccia</th><th>Valore Memorizzato</th><th>Match</th></tr>
                            ${tableRows}
                        </table>
                        <p><strong>Conclusione:</strong> ${allMatch ? 'Il sistema utilizza correttamente i dati reali dall\'interfaccia.' : 'Verificare la sincronizzazione dei dati.'}</p>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Errore confronto dati</h4>
                        <p><strong>Errore:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        // Test 5: Verifica Dati Hardcoded
        function testNoHardcodedData() {
            const resultDiv = document.getElementById('hardcodedResult');
            resultDiv.innerHTML = '<p>🔄 Verificando assenza dati hardcoded...</p>';
            
            try {
                const inputData = window.inertialMassState.lastInputData;
                if (!inputData) {
                    throw new Error('Esegui prima il Test 2 per memorizzare i dati');
                }
                
                // Valori hardcoded comuni da evitare
                const hardcodedValues = [
                    { value: 41.9028, field: 'Latitudine Roma (default)' },
                    { value: 12.4964, field: 'Longitudine Roma (default)' },
                    { value: 0.062, field: 'ag default' },
                    { value: 2.604, field: 'F0 default' },
                    { value: 0.268, field: 'TC default' },
                    { value: 'N/A', field: 'Placeholder non sostituito' }
                ];
                
                let hardcodedFound = [];
                let checks = [];
                
                // Controlla coordinate
                if (inputData.location.lat === 41.9028 && inputData.location.lon === 12.4964) {
                    hardcodedFound.push('Coordinate Roma (default)');
                }
                checks.push({ field: 'Coordinate', value: `${inputData.location.lat}, ${inputData.location.lon}`, status: inputData.location.lat !== 41.9028 ? '✅' : '⚠️' });
                
                // Controlla parametri sismici
                checks.push({ field: 'ag', value: inputData.seismic_params.ag, status: inputData.seismic_params.ag !== 0.062 ? '✅' : '⚠️' });
                checks.push({ field: 'F0', value: inputData.seismic_params.F0, status: inputData.seismic_params.F0 !== 2.604 ? '✅' : '⚠️' });
                checks.push({ field: 'TC*', value: inputData.seismic_params.TC, status: inputData.seismic_params.TC !== 0.268 ? '✅' : '⚠️' });
                
                const tableRows = checks.map(check => `
                    <tr>
                        <td>${check.field}</td>
                        <td>${check.value}</td>
                        <td>${check.status}</td>
                    </tr>
                `).join('');
                
                const resultClass = hardcodedFound.length === 0 ? 'result' : 'warning';
                const resultIcon = hardcodedFound.length === 0 ? '✅' : '⚠️';
                const resultMessage = hardcodedFound.length === 0 ? 
                    'Nessun valore hardcoded rilevato!' : 
                    `Rilevati ${hardcodedFound.length} possibili valori hardcoded`;
                
                resultDiv.innerHTML = `
                    <div class="${resultClass}">
                        <h4>${resultIcon} ${resultMessage}</h4>
                        <table class="data-table">
                            <tr><th>Campo</th><th>Valore</th><th>Status</th></tr>
                            ${tableRows}
                        </table>
                        ${hardcodedFound.length > 0 ? `<p><strong>Valori sospetti:</strong> ${hardcodedFound.join(', ')}</p>` : ''}
                        <p><strong>Conclusione:</strong> ${hardcodedFound.length === 0 ? 'Il sistema utilizza correttamente dati dinamici.' : 'Alcuni valori potrebbero essere hardcoded - verifica la configurazione.'}</p>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Errore verifica dati hardcoded</h4>
                        <p><strong>Errore:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        // Inizializza con dati di esempio
        updateSeismicParameters();
    </script>
</body>
</html>
