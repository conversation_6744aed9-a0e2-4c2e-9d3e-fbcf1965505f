<?php
// tools/test_backup_structure.php - Test per verificare la struttura del backup ZIP
// Percorso: tools/test_backup_structure.php

// Avvia sessione per simulare utente admin
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_admin';
$_SESSION['role'] = 'admin';

require_once __DIR__ . '/../includes/db_config.php';
require_once __DIR__ . '/../includes/logger.php';

/**
 * Test della struttura del backup ZIP
 */
function testBackupStructure() {
    echo "<h2>Test Struttura Backup ZIP</h2>";
    
    echo "<p>Avvio test backup diretto...</p>";
    
    // Simula una richiesta POST per il backup
    $_POST['action'] = 'backup'; // Simula richiesta POST
    
    // Includi direttamente il file di backup per testarlo
    ob_start();
    try {
        include __DIR__ . '/../api/backup_process_zip.php';
        $output = ob_get_clean();
        
        // Prova a decodificare la risposta JSON
        $result = json_decode($output, true);
        
        if (!$result || !$result['success']) {
            echo "<p style='color: red;'>Backup fallito: " . ($result['message'] ?? 'Output non JSON: ' . htmlspecialchars(substr($output, 0, 200))) . "</p>";
            return false;
        }
        
        echo "<p style='color: green;'>Backup completato con successo!</p>";
        echo "<ul>";
        echo "<li>File: " . $result['filename'] . "</li>";
        echo "<li>Dimensione: " . $result['size'] . "</li>";
        echo "<li>Metodo: " . $result['method'] . "</li>";
        echo "<li>File copiati: " . $result['files_count'] . "</li>";
        echo "</ul>";
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>Errore durante il backup: " . $e->getMessage() . "</p>";
        return false;
    }
    
    // Verifica la struttura del ZIP se esiste
    $backupPath = __DIR__ . '/../backups/' . $result['filename'];
    
    if (file_exists($backupPath) && class_exists('ZipArchive')) {
        echo "<h3>Analisi Struttura ZIP</h3>";
        
        $zip = new ZipArchive();
        if ($zip->open($backupPath) === TRUE) {
            echo "<p>Contenuto del ZIP:</p>";
            echo "<ul>";
            
            $hasNestedFolders = false;
            $structure = [];
            
            for ($i = 0; $i < $zip->numFiles; $i++) {
                $filename = $zip->getNameIndex($i);
                $structure[] = $filename;
                
                // Controlla se ci sono cartelle nidificate indesiderate
                if (preg_match('/^temp_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\//i', $filename)) {
                    $hasNestedFolders = true;
                }
                
                echo "<li>" . htmlspecialchars($filename) . "</li>";
                
                // Mostra solo i primi 20 file per evitare output troppo lungo
                if ($i >= 19) {
                    echo "<li>... e altri " . ($zip->numFiles - 20) . " file</li>";
                    break;
                }
            }
            
            $zip->close();
            echo "</ul>";
            
            // Risultato del test
            if ($hasNestedFolders) {
                echo "<p style='color: red; font-weight: bold;'>❌ PROBLEMA RILEVATO: Il ZIP contiene ancora cartelle nidificate!</p>";
                echo "<p>La struttura dovrebbe essere: database/, files/, backup_info.txt</p>";
                return false;
            } else {
                echo "<p style='color: green; font-weight: bold;'>✅ STRUTTURA CORRETTA: Nessuna cartella nidificata rilevata!</p>";
                
                // Verifica che esistano le cartelle principali
                $hasDatabase = false;
                $hasFiles = false;
                $hasBackupInfo = false;
                
                foreach ($structure as $path) {
                    if (strpos($path, 'database/') === 0) $hasDatabase = true;
                    if (strpos($path, 'files/') === 0) $hasFiles = true;
                    if ($path === 'backup_info.txt') $hasBackupInfo = true;
                }
                
                echo "<p>Verifica contenuto:</p>";
                echo "<ul>";
                echo "<li>" . ($hasDatabase ? '✅' : '❌') . " Cartella database/</li>";
                echo "<li>" . ($hasFiles ? '✅' : '❌') . " Cartella files/</li>";
                echo "<li>" . ($hasBackupInfo ? '✅' : '❌') . " File backup_info.txt</li>";
                echo "</ul>";
                
                return $hasDatabase && $hasFiles && $hasBackupInfo;
            }
        } else {
            echo "<p style='color: red;'>Impossibile aprire il file ZIP per l'analisi</p>";
            return false;
        }
    } else {
        echo "<p style='color: orange;'>File ZIP non trovato o ZipArchive non disponibile per l'analisi</p>";
        return true; // Consideriamo il test passato se il backup è stato creato
    }
}

// Esegui il test se chiamato direttamente
if (basename($_SERVER['PHP_SELF']) === 'test_backup_structure.php' || php_sapi_name() === 'cli') {
    echo "<!DOCTYPE html>";
    echo "<html><head><title>Test Backup Structure</title></head><body>";
    
    echo "<p>Inizio test...</p>";
    flush();
    
    try {
        $success = testBackupStructure();
        
        echo "<hr>";
        echo "<h3>Risultato Test</h3>";
        if ($success) {
            echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 TEST SUPERATO!</p>";
            echo "<p>La struttura del backup ZIP è corretta.</p>";
        } else {
            echo "<p style='color: red; font-size: 18px; font-weight: bold;'>❌ TEST FALLITO!</p>";
            echo "<p>Ci sono ancora problemi con la struttura del backup ZIP.</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Errore durante l'esecuzione del test: " . $e->getMessage() . "</p>";
    }
    
    echo "</body></html>";
}
?>