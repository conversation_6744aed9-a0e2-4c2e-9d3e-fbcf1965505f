# 📋 Indice Generale Documentazione ASDP
Ultimo aggiornamento: 15/06/2025 - v2.5.0

## 1. Documentazione Base
- [README.md](README.md) - **INIZIA QUI** - Panoramica completa della documentazione
- [STRUTTURA_PROGETTO.md](STRUTTURA_PROGETTO.md) - Struttura completa del progetto
- [00_funzionamento.md](00_funzionamento.md) - **AGGIORNATO v2.5.0** - Guida completa funzionamento sistema
- [01_panoramica.md](01_panoramica.md) - Introduzione al progetto
- [02_struttura.md](02_struttura.md) - Struttura dell'applicazione
- [03_componenti.md](03_componenti.md) - Componenti principali

## 2. Documentazione Tecnica
- [04_database.md](04_database.md) - Struttura e gestione del database
- [05_api.md](05_api.md) - Documentazione delle API
- [06_procedure.md](06_procedure.md) - Procedure operative
- [07_troubleshooting.md](07_troubleshooting.md) - Risoluzione problemi
- [08_sicurezza.md](08_sicurezza.md) - Misure di sicurezza
- [09_performance.md](09_performance.md) - Ottimizzazione delle prestazioni
- [10_metodo_calcolo.md](10_metodo_calcolo.md) - Metodo di calcolo sismico
- [14_massa_inerziale.md](14_massa_inerziale.md) - Modulo Massa Inerziale

## 3. Documentazione di Sviluppo
- [11_miglioramenti.md](11_miglioramenti.md) - Proposte di miglioramento
- [12_aggiornamenti.md](12_aggiornamenti.md) - Registro degli aggiornamenti
- [13_flussi_lavoro.md](13_flussi_lavoro.md) - Flussi di lavoro

## 4. File di Supporto
- [relazione_asdp.md](relazione_asdp.md) - Relazione generale ASDP
- [app_map.md](app_map.md) - Mappa dell'applicazione

## 5. Directory Speciali
- `/analisi_normative` - Analisi delle normative tecniche
- `/templates` - Template per generazione documentazione

## 6. File HTML Generati
- documentazione_tecnica.html - Documentazione tecnica completa
- relazione_tecnica.html - Relazione tecnica del progetto

## Note Importanti
1. La documentazione viene aggiornata regolarmente
2. I file HTML vengono generati automaticamente da generate_docs.php
3. La directory templates contiene i template per la generazione
4. Verificare sempre la data di ultimo aggiornamento di ogni file
5. **[15/06/2025] AGGIORNAMENTO COMPLETO v2.5.0: Aggiornato 00_funzionamento.md con stato reale sistema, modulo massa inerziale AI, architettura completa. Ottimizzazione progetto completata con pulizia 14 file obsoleti.**
6. **[12/12/2024] CONSOLIDAMENTO DOCUMENTAZIONE v2.4.1: Unificati tutti i report e aggiornamenti in 11_miglioramenti.md. Struttura cronologica, descrizioni concise, navigazione centralizzata.**
6. **[06/06/2025] FIX SISTEMA LOG E MERMAID: Risolti errori ZipArchive, JSON AJAX e diagrammi Mermaid. Sistema completamente operativo.**
7. **[05/06/2025] PULIZIA COMPLETA WORKSPACE: Eliminati file obsoleti, duplicati e di test. Riorganizzata struttura progetto. Aggiornata documentazione.**
8. **[05/06/2025] Implementato sistema a tre livelli LLM (Deepseek → Gemma3 → Locale) per calcolo massa inerziale. Corretti bug parametri sismici dinamici.**
9. **[07/05/2025] Completata analisi approfondita del sistema con identificazione problematiche e proposte di miglioramento.**
10. **[27/04/2025] Effettuata pulizia completa del sistema: database svuotato e rimossi file temporanei.**

## File Rimossi nella Pulizia del 12/12/2024
### Pulizia 05/06/2025
- File di test obsoleti (test_backup_*.php, test_*.php)
- Directory src/ e tests/ non utilizzate
- File duplicati (cache_service.php, Logger.php duplicati)
- Documentazione temporanea (nextsession.md, timesheet.md)
- Directory spaceinv/ (non correlata al progetto)
- File di debug e cache temporanei

### Consolidamento Documentazione 12/12/2024 (v2.4.1)
- File tecnici consolidati in 11_miglioramenti.md:
  - backup_system_final_solution.md → Sezione Sistema Backup
  - CORREZIONE_MASSA_INERZIALE.md → Sezione Modulo Massa Inerziale
  - SISTEMA_TRE_LIVELLI_LLM.md → Sezione Sistema a Tre Livelli
  - RICALCOLO_PARAMETRI_SISMICI.md → Sezione Parametri Sismici
  - pulizia_documentazione_report.md → Sezione Pulizia Documentazione

### Pulizia Documentazione 12/12/2024 (v2.4.0)
- Report fix temporanei completati (backup_system_fix_report.md, logs_scroll_*.md)
- File di sviluppo obsoleti (sviluppo_inerziale.md, database_massa_inerziale_report.md)
- Documentazione di fix implementati (logs_system_improvements.md, logs_visibility_fix.md)