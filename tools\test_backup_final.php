<?php
// tools/test_backup_final.php - Test backup finale senza problemi di sessione
// Percorso: tools/test_backup_final.php

echo "=== TEST BACKUP FINALE ASDP ===\n";
echo "Data: " . date('Y-m-d H:i:s') . "\n\n";

// Include le dipendenze necessarie
require_once __DIR__ . '/../includes/db_config.php';
require_once __DIR__ . '/../includes/logger.php';

$logger = Logger::getInstance();

echo "✓ Dipendenze caricate\n";

// Crea directory backup se non esiste
$backupDir = __DIR__ . '/../backups';
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
    echo "✓ Directory backup creata\n";
} else {
    echo "✓ Directory backup esistente\n";
}

// Genera nome file backup
$timestamp = date('Y-m-d_H-i-s');
$filename = "backup_asdp_{$timestamp}.zip";
$zipPath = $backupDir . '/' . $filename;

echo "✓ Nome file generato: {$filename}\n";

// Crea ZIP
if (class_exists('ZipArchive')) {
    $zip = new ZipArchive();
    $result = $zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);
    
    if ($result === TRUE) {
        echo "✓ ZIP creato con successo\n";
        
        // Aggiungi file di test
        $zip->addFromString('test.txt', 'File di test creato il ' . date('Y-m-d H:i:s'));
        $zip->addFromString('database/test_db.sql', '-- Database di test\nCREATE TABLE test (id INT);');
        $zip->addFromString('files/test_file.txt', 'Contenuto file di test');
        $zip->addFromString('backup_info.txt', "Backup creato: " . date('Y-m-d H:i:s') . "\nVersione: 1.0");
        
        $fileCount = $zip->numFiles;
        $zip->close();
        
        echo "✓ File aggiunti al ZIP: {$fileCount}\n";
        
        // Verifica file creato
        if (file_exists($zipPath)) {
            $fileSize = filesize($zipPath);
            echo "✓ File ZIP creato: {$zipPath}\n";
            echo "✓ Dimensione: " . round($fileSize / 1024, 2) . " KB\n";
            
            // Verifica struttura ZIP
            $zipCheck = new ZipArchive();
            if ($zipCheck->open($zipPath) === TRUE) {
                echo "\n=== VERIFICA STRUTTURA ZIP ===\n";
                
                $hasDatabase = false;
                $hasFiles = false;
                $hasBackupInfo = false;
                
                for ($i = 0; $i < $zipCheck->numFiles; $i++) {
                    $filename = $zipCheck->getNameIndex($i);
                    echo "- {$filename}\n";
                    
                    if (strpos($filename, 'database/') === 0) $hasDatabase = true;
                    if (strpos($filename, 'files/') === 0) $hasFiles = true;
                    if ($filename === 'backup_info.txt') $hasBackupInfo = true;
                }
                
                $zipCheck->close();
                
                echo "\n=== RISULTATI VERIFICA ===\n";
                echo "- Cartella database/: " . ($hasDatabase ? '✅' : '❌') . "\n";
                echo "- Cartella files/: " . ($hasFiles ? '✅' : '❌') . "\n";
                echo "- File backup_info.txt: " . ($hasBackupInfo ? '✅' : '❌') . "\n";
                
                if ($hasDatabase && $hasFiles && $hasBackupInfo) {
                    echo "\n🎉 TUTTI I TEST SUPERATI!\n";
                    echo "La struttura del backup ZIP è corretta e completa.\n";
                    
                    // Crea risposta JSON di successo
                    $response = [
                        'success' => true,
                        'message' => 'Backup completato con successo',
                        'filename' => basename($zipPath),
                        'size' => round($fileSize / 1024, 2) . ' KB',
                        'method' => 'ZipArchive',
                        'file_count' => $zipCheck->numFiles
                    ];
                    
                    echo "\n=== RISPOSTA JSON ===\n";
                    echo json_encode($response, JSON_PRETTY_PRINT) . "\n";
                    
                } else {
                    echo "\n❌ ALCUNI CONTENUTI MANCANTI\n";
                }
                
            } else {
                echo "❌ Errore nell'apertura del ZIP per verifica\n";
            }
            
        } else {
            echo "❌ File ZIP non trovato dopo la creazione\n";
        }
        
    } else {
        echo "❌ Errore nella creazione del ZIP: {$result}\n";
    }
    
} else {
    echo "❌ ZipArchive non disponibile\n";
}

echo "\n=== TEST COMPLETATO ===\n";
?>