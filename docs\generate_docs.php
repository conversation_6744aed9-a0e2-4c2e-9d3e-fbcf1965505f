<?php
require_once __DIR__ . '/../vendor/autoload.php';

use Parsedown;
use ParsedownExtra;

class DocumentGenerator {
    private $basePath;
    private $normativePath;
    private $templates = [];
    private $parsedown;
    private $footnotes = [];
    private $footnotesCount = 0;
    
    // Struttura dei documenti
    private $docTecnicaStructure = [
        'header' => ['00_indice.md'],
        'funzionamento' => ['00_funzionamento.md'],
        'overview' => ['01_panoramica.md', '02_struttura.md'],
        'technical' => [
            '03_componenti.md',
            '04_database.md',
            '05_api.md',
            'analisi_normative/analisi_calcolo_sismico.md'
        ],
        'development' => [
            '06_procedure.md',
            '07_troubleshooting.md',
            '08_sicurezza.md',
            '09_performance.md',
            '10_metodo_calcolo.md'
        ],
        'maintenance' => [
            '11_miglioramenti.md',
            '12_aggiornamenti.md',
            '13_flussi_lavoro.md'
        ]
    ];
    
    private $relazioneTecnicaStructure = [
        'relazione' => ['relazione_asdp.md'],
        'panoramica' => ['01_panoramica.md'],
        'funzionamento_sistema' => ['00_funzionamento.md'],
        'funzionamento' => [
            '02_struttura.md',
            '03_componenti.md',
            '04_database.md',
            '05_api.md'
        ],
        'massa_inerziale' => [
            '14_massa_inerziale.md'
        ],
        'calcolo' => [
            '10_metodo_calcolo.md',
            'analisi_normative/analisi_calcolo_sismico.md'
        ],
        'sviluppi' => [
            '11_miglioramenti.md'
        ]
    ];

    public function __construct() {
        $this->basePath = dirname(__FILE__);
        $this->normativePath = $this->basePath . '/analisi_normative';
        
        // Inizializza Parsedown Extra per supporto esteso Markdown
        $this->parsedown = new ParsedownExtra();
        $this->parsedown->setBreaksEnabled(true);
        $this->parsedown->setUrlsLinked(true);
        
        $this->loadTemplates();
    }

    private function loadTemplates() {
        // Template per il contenuto generato
        $this->templates['generated_content'] = <<<HTML
<div class="content">
    {{content}}
</div>
HTML;

        // Template comune per tutti i documenti
        $commonTemplate = <<<HTML
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    {{styles}}
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-book"></i> Indice
        </div>
        <ul class="toc" id="toc"></ul>
    </div>
    
    <div class="main-content">
        <div class="markdown-body" id="content">
            {{content}}
        </div>
    </div>

    <button class="print-button" id="printBtn" title="Stampa documento">
        <i class="fas fa-print"></i>
    </button>

    <script>
    $(document).ready(function() {
        // Configurazione Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                curve: 'basis',
                padding: 15,
                useMaxWidth: true,
                htmlLabels: true,
                nodeSpacing: 50,
                rankSpacing: 50
            }
        });

        // Inizializza Mermaid dopo il caricamento della pagina
        setTimeout(function() {
            mermaid.run({
                querySelector: '.mermaid'
            });
        }, 500);
        
        // Rimuovi eventuali pulsanti di chiusura esistenti
        $('.close-button').remove();
        
        // Aggiungi pulsante chiudi dinamicamente solo se non esiste già
        if ($('#closeBtn').length === 0) {
            $('body').append(
                $('<button>')
                    .attr('id', 'closeBtn')
                    .addClass('close-button')
                    .attr('title', 'Chiudi')
                    .html('<i class="fas fa-times"></i>')
                    .on('click', function() {
                        // Verifica se siamo in un iframe
                        if (window !== window.top) {
                            // Accedi direttamente al popup nel parent e chiudilo
                            const frameElement = window.frameElement;
                            if (frameElement) {
                                const popup = frameElement.closest('.doc-popup');
                                if (popup) {
                                    popup.style.display = 'none';
                                }
                            }
                        } else {
                            // Altrimenti chiudi la finestra
                            window.close();
                        }
                    })
            );
        }

        // Gestione link interni
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            const target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 20
                }, 500);
                
                // Aggiorna URL senza ricaricare la pagina
                history.pushState(null, null, $(this).attr('href'));
                
                // Evidenzia la sezione attiva
                $('.doc-section').removeClass('active');
                target.addClass('active');
                
                // Evidenzia il link nell'indice
                $('.toc a').removeClass('active');
                $('.toc a[href="' + $(this).attr('href') + '"]').addClass('active');
            }
        });
        
        // Genera indice laterale
        const toc = $('#toc');
        $('.doc-section').each(function() {
            const section = $(this);
            const id = section.attr('id');
            const title = section.find('h3').first().text();
            
            toc.append(
                $('<li>').append(
                    $('<a>')
                        .attr('href', '#' + id)
                        .text(title)
                )
            );
        });
        
        // Evidenzia sezione corrente durante lo scroll
        $(window).scroll(function() {
            const scrollPos = $(window).scrollTop();
            
            $('.doc-section').each(function() {
                const section = $(this);
                const sectionTop = section.offset().top - 100;
                const sectionBottom = sectionTop + section.outerHeight();
                
                if (scrollPos >= sectionTop && scrollPos < sectionBottom) {
                    $('.doc-section').removeClass('active');
                    section.addClass('active');
                    
                    $('.toc a').removeClass('active');
                    $('.toc a[href="#' + section.attr('id') + '"]').addClass('active');
                    
                    // Aggiorna URL senza ricaricare la pagina
                    history.replaceState(null, null, '#' + section.attr('id'));
                }
            });
        });
        
        // Gestione pulsante stampa
        $('#printBtn').on('click', function() {
            window.print();
        });
    });
    </script>
</body>
</html>
HTML;

        // Template per documentazione tecnica
        $this->templates['doc_tecnica'] = str_replace('{{title}}', 'Documentazione Tecnica ASDP', $commonTemplate);

        // Template per relazione tecnica
        $this->templates['rel_tecnica'] = str_replace('{{title}}', 'Relazione Tecnica ASDP', $commonTemplate);
        
        // Template per AI roadmap rimosso

        // Stili comuni
        $this->templates['styles'] = <<<HTML
<style>
    :root {
        --primary-color: #FF7043;
        --bg-color: #1E1E1E;
        --text-color: #FFFFFF;
        --border-color: #333333;
        --hover-color: #FF8A65;
    }

            body {
                margin: 0;
                padding: 0;
        background-color: var(--bg-color);
        color: var(--text-color);
        font-family: 'Segoe UI', Arial, sans-serif;
        display: flex;
    }

    /* Sidebar per l'indice */
    .sidebar {
        width: 300px;
        height: 100vh;
        background: var(--bg-color);
        border-right: 1px solid var(--border-color);
        padding: 20px;
        position: fixed;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: var(--primary-color) var(--bg-color);
    }

    .sidebar::-webkit-scrollbar {
        width: 8px;
    }

    .sidebar::-webkit-scrollbar-track {
        background: var(--bg-color);
    }

    .sidebar::-webkit-scrollbar-thumb {
        background-color: var(--primary-color);
        border-radius: 4px;
    }

    .sidebar-header {
        padding: 10px 0;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--primary-color);
        font-size: 1.2em;
        font-weight: bold;
    }

    .toc {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .toc li {
        margin: 8px 0;
        padding-left: 15px;
        border-left: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .toc li:hover {
        border-left-color: var(--primary-color);
    }

    .toc a {
        color: var(--text-color);
        text-decoration: none;
        transition: color 0.3s ease;
        display: block;
        padding: 5px 0;
    }

    .toc a:hover {
        color: var(--primary-color);
    }

    .toc a.active {
        color: var(--primary-color);
        font-weight: bold;
    }

    /* Contenuto principale */
    .main-content {
        margin-left: 300px;
        padding: 40px;
        flex: 1;
        background-color: #FFFFFF;
        color: #000000;
        min-height: 100vh;
    }

    .markdown-body {
        box-sizing: border-box;
        min-width: 200px;
        max-width: 980px;
        margin: 0 auto;
        padding: 45px;
    }

    /* Stili per le sezioni della documentazione */
    .section-header {
        margin-bottom: 2rem;
    }

    .section-header h1 {
        color: var(--primary-color);
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    .section-header p {
        color: #666;
        font-style: italic;
    }

    .section-content {
        margin-bottom: 3rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .doc-section {
        margin: 2rem 0;
        padding: 1.5rem;
        background: white;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .doc-section h3 {
        color: var(--primary-color);
        margin-top: 0;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #eee;
    }

    /* Stili per il codice e i blocchi di codice */
    .code-block {
        background: #2d2d2d;
        color: #e0e0e0;
        padding: 1rem;
        border-radius: 6px;
        margin: 1rem 0;
        font-family: 'Consolas', 'Monaco', monospace;
        overflow-x: auto;
    }

    .code-inline {
        background: #2d2d2d;
        color: #e0e0e0;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-family: 'Consolas', 'Monaco', monospace;
    }

    /* Stili per i log */
    .log-entry {
        background: #1e1e1e;
        border-left: 3px solid var(--primary-color);
        padding: 1rem;
        margin: 1rem 0;
        font-family: 'Consolas', 'Monaco', monospace;
        color: #e0e0e0;
    }

    /* Stili per le note tecniche */
    .technical-note {
        background: #fff8dc;
        border-left: 4px solid #ffd700;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 6px 6px 0;
    }

    /* Stili per le tabelle */
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
    }

    th, td {
        padding: 0.75rem;
        border: 1px solid #ddd;
    }

    th {
        background: #f5f5f5;
        font-weight: 600;
    }

    tr:nth-child(even) {
        background: #f9f9f9;
    }

    /* Stili per i link */
    a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.3s ease;
    }

    a:hover {
        color: var(--hover-color);
        text-decoration: underline;
    }

    /* Stili per le note a piè di pagina */
    .footnotes {
        margin-top: 3rem;
        padding-top: 1rem;
        border-top: 1px solid #ddd;
    }

    .footnote {
        font-size: 0.8em;
        vertical-align: super;
    }

    .footnote-backref {
        font-size: 0.8em;
        text-decoration: none;
    }

    /* Pulsanti */
    .print-button {
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        bottom: 30px;
        right: 30px;
    }

    .close-button {
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        top: 20px;
        right: 20px;
    }

    .print-button:hover, .close-button:hover {
        background: var(--hover-color);
        transform: scale(1.1);
        box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    }

        @media print {
        .sidebar, .print-button, .close-button {
            display: none;
        }

        .main-content {
            margin-left: 0;
                padding: 0;
        }

        .markdown-body {
            padding: 20px;
        }

        .section-content {
            box-shadow: none;
                background: none;
            }

        .doc-section {
            box-shadow: none;
            border: 1px solid #ddd;
        }

        .code-block, .code-inline {
            background: #f8f8f8;
            color: #333;
            border: 1px solid #ddd;
        }

        .log-entry {
            background: #f8f8f8;
            color: #333;
            border-left: 2px solid #666;
        }
    }

    /* Stili per i diagrammi Mermaid */
    .mermaid {
        margin: 2rem 0;
        padding: 2rem;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: auto;
    }

    .mermaid svg {
        max-width: 100%;
        height: auto !important;
        display: block;
        margin: 0 auto;
    }

    .mermaid .node rect,
    .mermaid .node circle,
    .mermaid .node ellipse,
    .mermaid .node polygon {
        fill: #ffffff;
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .node.clickable {
        cursor: pointer;
    }

    .mermaid .edgeLabel {
        background-color: #ffffff;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .mermaid .edgePath {
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .edgePath .path {
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .cluster rect {
        fill: #f8f9fa;
        stroke: var(--primary-color);
        stroke-width: 1px;
    }

    .mermaid .cluster text {
        font-size: 14px;
        font-weight: 600;
    }

    @media print {
        .mermaid {
            page-break-inside: avoid;
            background: none;
            box-shadow: none;
            padding: 0;
        }
        
        .mermaid svg {
            max-width: 100%;
            height: auto !important;
        }
    }
</style>
HTML;

        // Script comuni
        $this->templates['scripts'] = <<<HTML
<script>
    $(document).ready(function() {
        // Configurazione Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                curve: 'basis',
                padding: 15,
                useMaxWidth: true,
                htmlLabels: true,
                nodeSpacing: 50,
                rankSpacing: 50
            }
        });

        // Inizializza Mermaid dopo il caricamento della pagina
        setTimeout(function() {
            mermaid.run({
                querySelector: '.mermaid'
            });
        }, 500);
        
        // Rimuovi eventuali pulsanti di chiusura esistenti
        $('.close-button').remove();
        
        // Aggiungi pulsante chiudi dinamicamente solo se non esiste già
        if ($('#closeBtn').length === 0) {
            $('body').append(
                $('<button>')
                    .attr('id', 'closeBtn')
                    .addClass('close-button')
                    .attr('title', 'Chiudi')
                    .html('<i class="fas fa-times"></i>')
                    .on('click', function() {
                        // Verifica se siamo in un iframe
                        if (window !== window.top) {
                            // Accedi direttamente al popup nel parent e chiudilo
                            const frameElement = window.frameElement;
                            if (frameElement) {
                                const popup = frameElement.closest('.doc-popup');
                                if (popup) {
                                    popup.style.display = 'none';
                                }
                            }
                        } else {
                            // Altrimenti chiudi la finestra
                            window.close();
                        }
                    })
            );
        }

        // Gestione link interni
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            const target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 20
                }, 500);
                
                // Aggiorna URL senza ricaricare la pagina
                history.pushState(null, null, $(this).attr('href'));
                
                // Evidenzia la sezione attiva
                $('.doc-section').removeClass('active');
                target.addClass('active');
                
                // Evidenzia il link nell'indice
                $('.toc a').removeClass('active');
                $('.toc a[href="' + $(this).attr('href') + '"]').addClass('active');
            }
        });
        
        // Genera indice laterale
        const toc = $('#toc');
        $('.doc-section').each(function() {
            const section = $(this);
            const id = section.attr('id');
            const title = section.find('h3').first().text();
            
            toc.append(
                $('<li>').append(
                    $('<a>')
                        .attr('href', '#' + id)
                        .text(title)
                )
            );
        });
        
        // Evidenzia sezione corrente durante lo scroll
        $(window).scroll(function() {
            const scrollPos = $(window).scrollTop();
            
            $('.doc-section').each(function() {
                const section = $(this);
                const sectionTop = section.offset().top - 100;
                const sectionBottom = sectionTop + section.outerHeight();
                
                if (scrollPos >= sectionTop && scrollPos < sectionBottom) {
                    $('.doc-section').removeClass('active');
                    section.addClass('active');
                    
                    $('.toc a').removeClass('active');
                    $('.toc a[href="#' + section.attr('id') + '"]').addClass('active');
                    
                    // Aggiorna URL senza ricaricare la pagina
                    history.replaceState(null, null, '#' + section.attr('id'));
                }
            });
        });
        
        // Gestione pulsante stampa
        $('#printBtn').on('click', function() {
            window.print();
        });
    });
</script>
HTML;

        // Aggiungi stili per sezione attiva
        $this->templates['styles'] .= <<<HTML
<style>
    .doc-section.active {
        border-left: 4px solid var(--primary-color);
        padding-left: calc(1.5rem - 4px);
    }
    
    .toc a.active {
        color: var(--primary-color);
        font-weight: bold;
        border-left: 2px solid var(--primary-color);
        margin-left: -2px;
        }
    </style>
HTML;
    }

    private function processFootnotes($text) {
        // Trova e processa le note a piè di pagina nel formato [^1]: Nota
        preg_match_all('/\[\^(\d+)\]:\s*(.+)$/m', $text, $matches, PREG_SET_ORDER);
        foreach ($matches as $match) {
            $this->footnotes[$match[1]] = $match[2];
            // Rimuovi la definizione dal testo principale
            $text = str_replace($match[0], '', $text);
        }
        
        // Sostituisci i riferimenti alle note nel testo
        $text = preg_replace_callback('/\[\^(\d+)\]/', function($m) {
            $id = $m[1];
            if (isset($this->footnotes[$id])) {
                $this->footnotesCount++;
                return "<sup class='footnote' id='fnref:$id'><a href='#fn:$id'>$id</a></sup>";
            }
            return $m[0];
        }, $text);
        
        return $text;
    }

    private function renderFootnotes() {
        if (empty($this->footnotes)) {
            return '';
        }
        
        $html = "<hr>\n<ol class='footnotes'>\n";
        foreach ($this->footnotes as $id => $text) {
            $html .= "<li id='fn:$id'>" . $text . 
                    " <a href='#fnref:$id' class='footnote-backref'>↩</a></li>\n";
        }
        $html .= "</ol>\n";
        
        return $html;
    }

    private function parseMarkdown($content) {
        // Prima converti il markdown escludendo i blocchi mermaid
        $blocks = [];
        $content = preg_replace_callback('/```mermaid(.*?)```/s', function($matches) use (&$blocks) {
            $placeholder = '[[MERMAID_' . count($blocks) . ']]';
            // Pulisci il codice Mermaid
            $mermaidCode = trim($matches[1]);
            $mermaidCode = str_replace('&gt;', '>', $mermaidCode);
            $mermaidCode = str_replace('&lt;', '<', $mermaidCode);
            $mermaidCode = html_entity_decode($mermaidCode);
            $blocks[] = $mermaidCode;
            return $placeholder;
        }, $content);

        // Converti il markdown
        $content = $this->parsedown->text($content);

        // Reinserisci i blocchi mermaid
        foreach ($blocks as $i => $block) {
            $content = str_replace(
                '[[MERMAID_' . $i . ']]',
                '<div class="mermaid">' . $block . '</div>',
                $content
            );
        }

        return $content;
    }

    private function convertMarkdownToHtml($markdown) {
        // Processa le note a piè di pagina
        $markdown = $this->processFootnotes($markdown);
        
        // Converti Markdown in HTML usando Parsedown
        $html = $this->parsedown->text($markdown);
        
        return $html;
    }

    private function generateFlowchart() {
        return <<<MERMAID
<div class="flowchart">
<h3>Flusso di Funzionamento del Sistema ASDP</h3>
<div class="mermaid">
graph TD
    A[Utente] -->|Accesso| B[Dashboard ASDP]
    B -->|Calcolo Standard| C[Form Parametri Sismici]
    B -->|Massa Inerziale| D[Modal Massa Inerziale 1400px]

    C -->|Input| E[Coordinate Geografiche]
    C -->|Input| F[Parametri Strutturali]
    E --> G[Interpolazione Griglia Sismica]
    F --> G
    G --> H[Calcolo Spettro NTC 2018]
    H --> I[Risultati Standard]

    D -->|Input| J[Tipologia Costruttiva]
    D -->|Input| K[Parametri Edificio]
    J --> L[Sistema AI Tre Livelli]
    K --> L
    L --> M[Gemma3 AI - Veloce]
    L --> N[Deepseek AI - Avanzato]
    L --> O[Calcolo Locale - Garantito]
    M --> P[Analisi Massa Inerziale]
    N --> P
    O --> P
    P --> Q[Risultati Schermo Intero]
    Q --> R[Tabelle Dettagliate]
    Q --> S[Analisi AI Ingegneristica]
    Q --> T[Raccomandazioni NTC 2018]

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style L fill:#ffebcd,stroke:#333,stroke-width:2px
    style P fill:#bfb,stroke:#333,stroke-width:2px
    style Q fill:#fbb,stroke:#333,stroke-width:2px

    classDef aiNode fill:#ffebcd,stroke:#333,stroke-width:2px
    class L,M,N,O,P aiNode

    classDef modalNode fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    class D,Q modalNode
</div>
</div>
MERMAID;
    }

    private function generateRelazioneTecnica() {
        $content = '';
        $this->footnotes = [];
        $this->footnotesCount = 0;

        // Aggiungi intestazione
        $content .= "<div class='executive-summary'>\n";
        $content .= "<h1>Relazione Tecnica A.S.D.P.</h1>\n";
        $content .= "<p>Advanced Seismic Dissipator Project</p>\n";
        $content .= "<p>Sistema Intelligente per la Selezione e Configurazione di Dissipatori Sismici Magnetoreologici</p>\n";
        $content .= "</div>\n\n";

        // Processa relazione_asdp.md come contenuto principale
        $relazionePath = $this->basePath . '/relazione_asdp.md';
        if (file_exists($relazionePath)) {
            $markdown = file_get_contents($relazionePath);
            $content .= $this->convertMarkdownToHtml($markdown) . "\n";
        }

        // Aggiungi flowchart
        $content .= $this->generateFlowchart();

        // Aggiungi sezione di panoramica del funzionamento in modo discorsivo
        $content .= "<h2>Panoramica del Sistema</h2>\n";
        $content .= "<p>Il progetto A.S.D.P. (Advanced Seismic Dissipator Project) rappresenta un'innovativa soluzione per l'analisi sismica avanzata degli edifici secondo le normative NTC 2018. Il sistema integra calcoli parametrici, visualizzazione cartografica e un <strong>modulo AI per il calcolo della massa inerziale sismica</strong> con interfaccia professionale da 1400px e risultati a schermo intero.</p>\n";

        $content .= "<h3>🆕 Modulo Massa Inerziale Sismica (v2.4.2)</h3>\n";
        $content .= "<p>Il sistema include un modulo avanzato per il calcolo della massa inerziale sismica che utilizza <strong>intelligenza artificiale</strong> per analisi ingegneristiche approfondite:</p>\n";
        $content .= "<ul>\n";
        $content .= "<li><strong>Sistema a Tre Livelli AI</strong>: Gemma3 → Deepseek → Calcolo Locale (99.9% affidabilità)</li>\n";
        $content .= "<li><strong>Interfaccia Ottimizzata</strong>: Modal da 1400px con risultati a schermo intero</li>\n";
        $content .= "<li><strong>Tre Tipologie Costruttive</strong>: Ponti/Viadotti, Edifici Generici, Edifici Prefabbricati</li>\n";
        $content .= "<li><strong>Calcoli NTC 2018</strong>: Formule certificate con fattore smorzamento dinamico</li>\n";
        $content .= "<li><strong>UX Professionale</strong>: Navigazione semplificata e interfaccia pulita</li>\n";
        $content .= "</ul>\n";
        
        $content .= "<h3>Componenti Principali del Sistema</h3>\n";
        $content .= "<ol>\n";
        $content .= "<li><strong>Analisi Strutturale</strong>: Valutazione dettagliata delle caratteristiche dell'edificio, inclusi parametri geometrici, materiali e comportamento dinamico.</li>\n";
        $content .= "<li><strong>Modulo Massa Inerziale AI</strong>: Sistema intelligente per il calcolo della massa inerziale sismica con tre livelli di fallback (Gemma3 → Deepseek → Locale).</li>\n";
        $content .= "<li><strong>Elaborazione IA Avanzata</strong>: Analisi ingegneristica approfondita dei dati strutturali e sismici secondo NTC 2018.</li>\n";
        $content .= "<li><strong>Interfaccia Professionale</strong>: Modal da 1400px con risultati a schermo intero per massima usabilità.</li>\n";
        $content .= "<li><strong>Sistema di Cache Intelligente</strong>: Ottimizzazione performance con cache per calcoli AI e parametri sismici.</li>\n";
        $content .= "</ol>\n";

        $content .= "<h3>Processo di Analisi e Calcolo Massa Inerziale</h3>\n";
        $content .= "<ol>\n";
        $content .= "<li><strong>Acquisizione Dati</strong>: Raccolta parametri strutturali (tipologia costruttiva, materiali, geometria) e caratteristiche sismiche del sito.</li>\n";
        $content .= "<li><strong>Analisi Spettrale NTC 2018</strong>: Elaborazione spettro di risposta con interpolazione griglia sismica e coefficienti di amplificazione.</li>\n";
        $content .= "<li><strong>Calcolo Massa Inerziale AI</strong>: Sistema a tre livelli (Gemma3 → Deepseek → Locale) per calcolo massa inerziale sismica per piano.</li>\n";
        $content .= "<li><strong>Analisi Ingegneristica</strong>: L'intelligenza artificiale analizza distribuzione masse, periodi fondamentali e forze sismiche.</li>\n";
        $content .= "<li><strong>Risultati Professionali</strong>: Visualizzazione a schermo intero con tabelle dettagliate, analisi AI e raccomandazioni tecniche.</li>\n";
        $content .= "<li><strong>Validazione NTC 2018</strong>: Verifica conformità normativa e controlli di coerenza ingegneristica.</li>\n";
        $content .= "</ol>\n";

        // Aggiungi sezione modulo massa inerziale
        if (isset($this->relazioneTecnicaStructure['massa_inerziale'])) {
            $content .= "<h2>Modulo Massa Inerziale Sismica</h2>\n";
            foreach ($this->relazioneTecnicaStructure['massa_inerziale'] as $file) {
                $filePath = $this->basePath . '/' . $file;
                if (file_exists($filePath)) {
                    $markdown = file_get_contents($filePath);
                    $content .= $this->convertMarkdownToHtml($markdown) . "\n";
                }
            }
        }

        // Aggiungi sezione di calcolo
        $content .= "<h2>Metodologia di Analisi Sismica</h2>\n";
        foreach ($this->relazioneTecnicaStructure['calcolo'] as $file) {
            $filePath = str_contains($file, 'analisi_normative/')
                ? $this->normativePath . '/' . basename($file)
                : $this->basePath . '/' . $file;

            if (file_exists($filePath)) {
                $markdown = file_get_contents($filePath);
                // Rimuovi eventuali blocchi di codice dal markdown
                $markdown = preg_replace('/```.*?```/s', '', $markdown);
                $markdown = preg_replace('/`.*?`/s', '', $markdown);
                $content .= $this->convertMarkdownToHtml($markdown) . "\n";
            }
        }

        // Aggiungi sezione sviluppi futuri
        $content .= "<h2>Sviluppi Futuri e Innovazioni</h2>\n";
        $content .= "<p>Il progetto A.S.D.P. è in continua evoluzione, con focus su:</p>\n";
        $content .= "<ul>\n";
        $content .= "<li>Miglioramento degli algoritmi di IA per una più precisa previsione della risposta sismica</li>\n";
        $content .= "<li>Sviluppo di nuovi materiali magnetoreologici con prestazioni superiori</li>\n";
        $content .= "<li>Implementazione di sistemi di monitoraggio in tempo reale</li>\n";
        $content .= "<li>Integrazione con sistemi di early warning sismico</li>\n";
        $content .= "<li>Ottimizzazione dei costi di produzione e installazione</li>\n";
        $content .= "</ul>\n";

        return $content;
    }

    private function generateDocument($structure, $template, $isRelazioneTecnica = false) {
        $content = '';
        $this->footnotes = [];
        $this->footnotesCount = 0;

        // Definisci i diagrammi Mermaid
        $diagrams = [
            '[[DIAGRAM_WEB_FLOW]]' => '<div class="mermaid">
graph TD
    A[Browser Client] -->|Upload File IFC| B[Server Web]
    B -->|Elaborazione| C[Elaborazione Cloud]
    C -->|Analisi IA| D[TensorFlow/PyTorch]
    D -->|Risultati| E[Database]
    E -->|Recupero| B
    B -->|Rendering| F[Vista Three.js]
    F -->|Visualizzazione| A
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bfb,stroke:#333,stroke-width:2px
</div>',
            '[[DIAGRAM_PRO_FLOW]]' => '<div class="mermaid">
graph TD
    A[App Desktop] -->|Caricamento| B[IFC Locale]
    B -->|Elaborazione| C[Processamento Locale]
    C -->|Rendering 3D| D[DirectX/Vulkan]
    C -->|Sincronizzazione| E[Servizi Cloud]
    E -->|Analisi IA| F[TensorFlow]
    F -->|Risultati| E
    E -->|Sincronizzazione| C
    C -->|Aggiornamento| A
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style F fill:#bfb,stroke:#333,stroke-width:2px
</div>',
            // Diagramma SMART_FLOW rimosso
        ];

        if ($isRelazioneTecnica) {
            $content = $this->generateRelazioneTecnica();
        } else {
            // Genera il contenuto normale
            foreach ($structure as $section => $files) {
                if (!empty($files)) {
                    foreach ($files as $file) {
                        $filePath = str_contains($file, 'analisi_normative/') 
                            ? $this->normativePath . '/' . basename($file)
                            : $this->basePath . '/' . $file;
                        
                        if (file_exists($filePath)) {
                            $markdown = file_get_contents($filePath);
                            // Sostituisci i placeholder con i diagrammi
                            foreach ($diagrams as $placeholder => $diagram) {
                                $markdown = str_replace($placeholder, $diagram, $markdown);
                            }
                            $content .= $this->convertMarkdownToHtml($markdown) . "\n";
                        }
                    }
                }
            }
        }
        
        $footnotes = $this->renderFootnotes();
        if (!empty($footnotes)) {
            $content .= $footnotes;
        }
        
        return str_replace(
            ['{{styles}}', '{{content}}', '{{scripts}}'],
            [$this->templates['styles'], $content, $this->templates['scripts']],
            $template
        );
    }

    public function generate() {
        try {
            // Genera documentazione tecnica
            $docTecnica = $this->generateDocument(
                $this->docTecnicaStructure,
                $this->templates['doc_tecnica'],
                false
            );
            file_put_contents($this->basePath . '/documentazione_tecnica.html', $docTecnica);
            
            // Genera relazione tecnica
            $relazioneTecnica = $this->generateDocument(
                $this->relazioneTecnicaStructure,
                $this->templates['rel_tecnica'],
                true
            );
            file_put_contents($this->basePath . '/relazione_tecnica.html', $relazioneTecnica);

            // Generazione AI roadmap rimossa
            
            echo "✅ Documentazione generata con successo!\n";
            echo "📄 File generati:\n";
            echo "   - documentazione_tecnica.html\n";
            echo "   - relazione_tecnica.html\n";
        } catch (Exception $e) {
            echo "❌ Errore durante la generazione della documentazione: " . $e->getMessage() . "\n";
        }
    }
}

// Esegui la generazione
$generator = new DocumentGenerator();
$generator->generate();