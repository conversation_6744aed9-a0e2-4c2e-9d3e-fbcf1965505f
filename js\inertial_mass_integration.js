/**
 * inertial_mass_integration.js - Integrazione del modulo massa inerziale in ASDP
 * Path: /js/inertial_mass_integration.js
 */

// Funzione helper per trovare il pulsante "Ricalcola Parametri"
function findRecalculateButton() {
    console.log('ASDP_IM: findRecalculateButton called');
    try {
        // Prova prima con il selettore :has() che è più specifico se l'icona è presente
        let button = document.querySelector('button:has(i.fas.fa-sync-alt)');
        if (button) {
            console.log('ASDP_IM: Found recalc button by icon:', button);
            return button;
        }
    } catch (e) {
        // :has() potrebbe non essere supportato o l'icona potrebbe mancare, procedi con la ricerca testuale
        console.warn('ASDP_IM: Selector button:has(i.fas.fa-sync-alt) failed, trying text search.', e);
    }

    // Fallback: cerca per contenuto testuale
    const allButtons = document.querySelectorAll('button');
    // console.log(`ASDP_IM: Found ${allButtons.length} buttons to check for text.`); // Log molto verboso, commentato
    for (const btn of allButtons) {
        if (btn.textContent && btn.textContent.includes('Ricalcola Parametri')) {
            console.log('ASDP_IM: Found recalc button by text:', btn);
            return btn;
        }
    }
    console.log('ASDP_IM: Recalculate button NOT found.');
    return null; // Bottone non trovato
}

// Funzione per creare e aggiungere il pulsante Massa Inerziale
function addInertialMassButton(anchorButton) {
    console.log('ASDP_IM: addInertialMassButton called. Anchor provided:', anchorButton);
    if (document.querySelector('.inertial-mass-btn')) {
        console.log('ASDP_IM: Inertial mass button already exists.');
        return; 
    }

    const inertialMassButton = document.createElement('button');
    inertialMassButton.className = 'btn btn-primary inertial-mass-btn';
    inertialMassButton.style.marginTop = '10px'; 
    inertialMassButton.style.width = '100%';
    inertialMassButton.style.display = 'flex';
    inertialMassButton.style.alignItems = 'center';
    inertialMassButton.style.justifyContent = 'center';
    inertialMassButton.innerHTML = '<i class="fas fa-balance-scale" style="margin-right: 8px;"></i> Calcolo Massa Inerziale';
    inertialMassButton.onclick = openInertialMassModal;

    if (anchorButton) {
        const buttonContainer = anchorButton.closest('.param-group') || anchorButton.parentElement;
        if (buttonContainer) {
            console.log('ASDP_IM: Appending inertial mass button to anchor container:', buttonContainer);
            buttonContainer.appendChild(inertialMassButton);
            return;
        } else {
            console.log('ASDP_IM: Anchor button provided, but no suitable container found. Falling back.');
        }
    }
    
    const seismicSection = document.querySelector('.seismic-params');
    if (seismicSection) {
        console.log('ASDP_IM: Appending inertial mass button to .seismic-params section:', seismicSection);
        const container = document.createElement('div');
        container.className = 'param-group'; 
        container.style.marginTop = '20px';
        container.appendChild(inertialMassButton);
        seismicSection.appendChild(container);
    } else {
        console.warn('ASDP_IM: Sezione parametri sismici (.seismic-params) non trovata. Il pulsante potrebbe non essere visibile o essere in una posizione inattesa.');
        // Come estremo fallback per debug, si potrebbe aggiungere a document.body, ma è meglio evitare in produzione.
        // console.log('ASDP_IM: Super fallback: Appending to document.body FOR DEBUGGING ONLY');
        // document.body.appendChild(inertialMassButton);
    }
}

// Funzione per aprire la modale massa inerziale
function openInertialMassModal() {
    console.log('Apertura modulo massa inerziale...');
    
    // Recupera i dati sismici aggiornati da ASDP
    const seismicData = getCurrentSeismicData();
    
    // Controlla se la modale è già stata caricata
    if (document.getElementById('inertialMassModal')) {
        console.log('Modale già caricata, apertura diretta...');
        // Verifica che la funzione sia disponibile prima di chiamarla
        if (typeof window.initInertialMassModal === 'function') {
            window.initInertialMassModal(seismicData);
        } else {
            console.log('Funzione initInertialMassModal non ancora disponibile, ricarico il modulo...');
            // Rimuovi la modale esistente e ricarica tutto
            const existingModal = document.getElementById('inertialMassModal');
            if (existingModal) {
                existingModal.remove();
            }
            loadInertialMassModule(seismicData);
        }
    } else {
        // Verifica se il modulo è già stato caricato
        if (typeof window.initInertialMassModal !== 'function') {
            // Carica dinamicamente i file necessari
            loadInertialMassModule(seismicData);
        } else {
            // Apri direttamente la modale
            window.initInertialMassModal(seismicData);
        }
    }
}

// Funzione per caricare dinamicamente il modulo
function loadInertialMassModule(seismicData) {
    console.log('Caricamento modulo massa inerziale...');

    // Carica il CSS principale con priorità massima e timestamp per evitare cache
    const cssLink = document.createElement('link');
    cssLink.rel = 'stylesheet';
    cssLink.href = '/progetti/asdp/inertial_mass/assets/css/modal.css?v=' + Date.now();
    cssLink.setAttribute('data-priority', 'high');
    document.head.appendChild(cssLink);

    // Carica il CSS specifico per i dissipatori sismici
    const damperCssLink = document.createElement('link');
    damperCssLink.rel = 'stylesheet';
    damperCssLink.href = '/progetti/asdp/inertial_mass/assets/css/damper_styles.css?v=' + Date.now();
    damperCssLink.setAttribute('data-priority', 'high');
    document.head.appendChild(damperCssLink);

    // Carica anche il debug script in modalità sviluppo
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const debugScript = document.createElement('script');
        debugScript.src = '/progetti/asdp/inertial_mass/debug_modal.js';
        document.head.appendChild(debugScript);
        console.log('Debug script caricato per ambiente di sviluppo');
    }
    
    // Carica il modal.php tramite AJAX
    const xhr = new XMLHttpRequest();
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                // Inserisci la modale nel DOM
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = xhr.responseText;
                document.body.appendChild(tempDiv);
                
                // Carica il JavaScript
                const script = document.createElement('script');
                script.src = '/progetti/asdp/inertial_mass/assets/js/modal.js';
                script.onload = function() {
                    // Quando il JavaScript è caricato, apri la modale
                    if (typeof window.initInertialMassModal === 'function') {
                        window.initInertialMassModal(seismicData);
                    } else {
                        console.error('Errore: initInertialMassModal non è disponibile');
                        alert('Errore nel caricamento del modulo massa inerziale');
                    }
                };
                document.body.appendChild(script);
            } else {
                console.error('Errore nel caricamento della modale:', xhr.status);
                alert('Errore nel caricamento del modulo massa inerziale');
            }
        }
    };
    xhr.open('GET', '/progetti/asdp/inertial_mass/modal.php', true);
    xhr.send();
}

// Aggiungi un pulsante sotto la sezione del ricalcolo parametri sismici
document.addEventListener('DOMContentLoaded', function() {
    console.log('ASDP_IM: DOMContentLoaded event fired.');
    // Rimuovi il pulsante dalla toolbar se esiste (pulizia da vecchie versioni)
    const toolbarButton = document.querySelector('.toolbar-button[title="Calcolo Massa Inerziale"]');
    if (toolbarButton) {
        console.log('ASDP_IM: Removing old toolbar button.');
        toolbarButton.remove();
    }
    
    const recalcButton = findRecalculateButton();
    addInertialMassButton(recalcButton); 
    
    console.log('ASDP_IM: Setting up MutationObserver.');
    const observer = new MutationObserver(function(mutations) {
        // Non loggare ogni mutazione, può essere troppo verboso.
        // console.log('ASDP_IM: MutationObserver detected DOM change.'); 
        let relevantChange = mutations.some(m => m.type === 'childList' && m.addedNodes.length > 0);

        if (relevantChange) {
            if (!document.querySelector('.inertial-mass-btn')) {
                console.log('ASDP_IM: Inertial mass button not found in observer, attempting to add.');
                const currentRecalcButton = findRecalculateButton();
                addInertialMassButton(currentRecalcButton);
            } else {
                // console.log('ASDP_IM: Inertial mass button already present in observer.');
            }
        }
    });
    
    observer.observe(document.body, { childList: true, subtree: true });
});

/**
 * Recupera i dati sismici correnti dall'interfaccia ASDP
 * @returns {Object} Dati sismici aggiornati
 */
function getCurrentSeismicData() {
    // Recupera i dati dai campi dell'interfaccia ASDP
    const coordinates = {
        lat: parseFloat(document.getElementById('latitude')?.value || document.getElementById('project-lat')?.value || 41.9028),
        lon: parseFloat(document.getElementById('longitude')?.value || document.getElementById('project-lon')?.value || 12.4964)
    };
    
    // Recupera la categoria del suolo SELEZIONATA (non il testo visualizzato)
    const soilCategorySelect = document.getElementById('soil-category');
    const selectedSoilCategory = soilCategorySelect?.value || 'C';
    
// Recupera i parametri sismici calcolati più recenti
// Prova prima SLV (Stato Limite di salvaguardia della Vita) che è il più comune
let ag = parseFloat(document.getElementById('slv-ag')?.textContent || 
                   document.getElementById('seismic-ag')?.textContent || 0.062);
let f0 = parseFloat(document.getElementById('slv-f0')?.textContent || 
                   document.getElementById('seismic-f0')?.textContent || 2.604);
let tc = parseFloat(document.getElementById('slv-tc')?.textContent || 
                   document.getElementById('seismic-tc')?.textContent || 0.268);
    
// Recupera la zona sismica
const zone = document.getElementById('seismic-zone')?.textContent || '3';

// Recupera il fattore di smorzamento dall'interfaccia principale
const dampingInput = document.getElementById('damping');
const damping = dampingInput ? parseFloat(dampingInput.value) : 5.0; // Default a 5.0 se non trovato
console.log('Fattore di smorzamento recuperato:', damping + '%');

// Recupera il fattore di struttura dall'interfaccia principale
const qFactorInput = document.getElementById('q-factor');
const qFactor = qFactorInput ? parseFloat(qFactorInput.value) : 1.0; // Default a 1.0 se non trovato
console.log('Fattore di struttura recuperato:', qFactor);

    const seismicData = {
        lat: coordinates.lat,
        lon: coordinates.lon,
        zone: zone,
        ag: ag,
        F0: f0,
        TC: tc,
        soil_category: selectedSoilCategory,
        damping: damping,
        q_factor: qFactor // Aggiunto q_factor
    };
    
    console.log('Dati sismici recuperati per massa inerziale:', seismicData);
    return seismicData;
}

// Espone le funzioni globalmente per permettere l'aggiornamento dopo il ricalcolo
window.getCurrentSeismicData = getCurrentSeismicData;
window.openInertialMassModal = openInertialMassModal;

// Nota: initInertialMassModal viene esposta dal file modal.js quando viene caricato dinamicamente
