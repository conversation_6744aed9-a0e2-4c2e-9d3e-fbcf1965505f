<?php
// tools/test_output_file.php - Test con output su file
// Percorso: tools/test_output_file.php

// Avvia output buffering per catturare tutto
ob_start();

echo "=== TEST FINALE BACKUP ASDP ===\n";
echo "Data: " . date('Y-m-d H:i:s') . "\n\n";

// Configura sessione admin
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_admin';
$_SESSION['role'] = 'admin';

echo "✓ Sessione admin configurata\n";

// Simula richiesta POST
$_POST['action'] = 'backup';

echo "✓ Richiesta POST simulata\n";

try {
    // Cattura output del backup
    ob_start();
    include __DIR__ . '/../api/backup_process_zip.php';
    $backupOutput = ob_get_clean();
    
    echo "✓ Script di backup eseguito\n";
    echo "Output backup: " . $backupOutput . "\n\n";
    
    // Decodifica risposta JSON
    $result = json_decode($backupOutput, true);
    
    if (!$result) {
        echo "❌ ERRORE: Output non è JSON valido\n";
        echo "Output ricevuto: " . substr($backupOutput, 0, 500) . "...\n";
    } else {
        if (!$result['success']) {
            echo "❌ ERRORE: Backup fallito\n";
            echo "Messaggio: " . $result['message'] . "\n";
        } else {
            echo "✅ BACKUP COMPLETATO CON SUCCESSO!\n\n";
            echo "Dettagli backup:\n";
            echo "- File: " . $result['filename'] . "\n";
            echo "- Dimensione: " . $result['size'] . "\n";
            echo "- Metodo: " . $result['method'] . "\n";
            echo "- Numero file: " . $result['file_count'] . "\n";
            
            // Verifica file esistente
            $backupPath = __DIR__ . '/../backups/' . $result['filename'];
            if (file_exists($backupPath)) {
                echo "✓ File backup trovato: " . $backupPath . "\n";
                echo "✅ TEST COMPLETATO CON SUCCESSO!\n";
            } else {
                echo "❌ File backup non trovato: " . $backupPath . "\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERRORE DURANTE IL TEST: " . $e->getMessage() . "\n";
}

// Cattura tutto l'output
$fullOutput = ob_get_clean();

// Scrive su file
file_put_contents(__DIR__ . '/test_results.txt', $fullOutput);

echo $fullOutput;
?>