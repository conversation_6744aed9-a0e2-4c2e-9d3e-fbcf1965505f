<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Generazione Report - ASDP v2.5.0</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .button {
            background: #FF7043;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #FF8A65;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .error {
            margin: 10px 0;
            padding: 10px;
            background: #ffe8e8;
            border-left: 4px solid #f44336;
        }
        .info {
            margin: 10px 0;
            padding: 10px;
            background: #e8f4fd;
            border-left: 4px solid #2196f3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Generazione Report Massa Inerziale v2.5.0</h1>
        <p><strong>Data Test:</strong> <span id="testDate"></span></p>
        
        <div class="test-section">
            <h3>📋 Test 1: Caricamento Template</h3>
            <p>Verifica che il template HTML sia accessibile e caricabile.</p>
            <button class="button" onclick="testTemplateLoading()">Testa Caricamento Template</button>
            <div id="templateResult"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 Test 2: Simulazione Dati</h3>
            <p>Simula dati di input e risultati per testare la generazione del report.</p>
            <button class="button" onclick="simulateData()">Simula Dati di Test</button>
            <div id="dataResult"></div>
        </div>
        
        <div class="test-section">
            <h3>🎯 Test 3: Generazione Report Completo</h3>
            <p>Genera un report completo con dati simulati.</p>
            <button class="button" onclick="generateTestReport()">Genera Report di Test</button>
            <div id="reportResult"></div>
        </div>
        
        <div class="test-section">
            <h3>📄 Test 4: Apertura in Nuova Finestra</h3>
            <p>Testa l'apertura del report in una nuova finestra del browser.</p>
            <button class="button" onclick="testWindowOpening()">Apri Report in Nuova Finestra</button>
            <div id="windowResult"></div>
        </div>
        
        <div class="info">
            <h4>ℹ️ Informazioni Test</h4>
            <p>Questi test verificano la funzionalità di generazione report senza richiedere un calcolo completo della massa inerziale.</p>
            <p>I test utilizzano dati simulati per verificare che tutte le funzioni JavaScript funzionino correttamente.</p>
        </div>
    </div>

    <script>
        // Imposta la data del test
        document.getElementById('testDate').textContent = new Date().toLocaleString('it-IT');
        
        // Dati simulati per i test - struttura corretta
        const mockInputData = {
            location: {
                lat: 41.9028,
                lon: 12.4964
            },
            seismic_params: {
                zone: 'Zona 2',
                ag: 0.15,
                F0: 2.5,
                TC: 0.28,
                soil_category: 'B',
                damping: 5,
                q_factor: 3.9
            },
            building: {
                construction_category: 'building',
                structure_type: 'concrete',
                slab_type: 'hollow_brick',
                construction_year: 2010,
                floors: []
            }
        };
        
        const mockResults = {
            calculation_id: 'test_calc_' + Date.now(),
            total_mass: 450.75,
            period: 0.85,
            total_force: 285.4,
            response_spectrum: 0.63,
            floor_forces: [
                { level: 'Piano 1', mass: 150.25, height: 3.5, force: 95.1 },
                { level: 'Piano 2', mass: 150.25, height: 7.0, force: 95.1 },
                { level: 'Piano 3', mass: 150.25, height: 10.5, force: 95.2 }
            ],
            damper_recommendations: {
                required_dissipation: 85.6,
                optimal_combination: {
                    dampers: [
                        { type: 'A', capacity_each: 500, quantity: 1, total_capacity: 500 }
                    ],
                    total_dampers: 1,
                    total_capacity: 500,
                    efficiency_ratio: 584.1
                },
                technical_explanation: 'ANALISI TECNICA DISSIPATORI SISMICI:\n\n🏗️ CARATTERISTICHE STRUTTURALI:\n• Massa inerziale totale: 450.75 tonnellate\n• Forza sismica di progetto: 285.4 kN\n• Accelerazione al suolo (ag): 0.15 g\n• Tipologia strutturale: Concrete\n\n⚙️ CRITERIO DI SELEZIONE:\n• Obiettivo: Riduzione 30% della risposta sismica\n• Dissipazione richiesta: 86 kN\n• Strategia: Minimizzazione numero dissipatori\n• Efficienza raggiunta: 584.1%',
                building_analysis: [
                    'Edificio di medie dimensioni: combinazione equilibrata di dissipatori consigliata',
                    'Struttura in cemento armato: dissipatori migliorano significativamente le prestazioni sismiche'
                ]
            },
            llm_analysis: 'Analisi AI simulata per test: L\'edificio presenta caratteristiche strutturali standard per la zona sismica di riferimento. Le raccomandazioni per i dissipatori sismici sono ottimizzate per garantire la massima efficienza con il minimo investimento.'
        };
        
        // Test 1: Caricamento Template
        async function testTemplateLoading() {
            const resultDiv = document.getElementById('templateResult');
            resultDiv.innerHTML = '<p>🔄 Testando caricamento template...</p>';
            
            try {
                const response = await fetch('/progetti/asdp/inertial_mass/report_template.html');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const templateContent = await response.text();
                const templateSize = (templateContent.length / 1024).toFixed(2);
                
                resultDiv.innerHTML = `
                    <div class="result">
                        <h4>✅ Template caricato con successo!</h4>
                        <p><strong>Dimensione:</strong> ${templateSize} KB</p>
                        <p><strong>Contiene placeholder:</strong> ${templateContent.includes('{{TOTAL_MASS}}') ? 'Sì' : 'No'}</p>
                        <p><strong>Contiene CSS:</strong> ${templateContent.includes('<style>') ? 'Sì' : 'No'}</p>
                        <p><strong>Contiene JavaScript:</strong> ${templateContent.includes('<script>') ? 'Sì' : 'No'}</p>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Errore caricamento template</h4>
                        <p><strong>Errore:</strong> ${error.message}</p>
                        <p><strong>Verifica:</strong> Il file report_template.html esiste nella cartella inertial_mass?</p>
                    </div>
                `;
            }
        }
        
        // Test 2: Simulazione Dati
        function simulateData() {
            const resultDiv = document.getElementById('dataResult');
            
            // Simula lo stato del modulo
            window.inertialMassState = {
                lastInputData: mockInputData,
                lastCalculationResults: mockResults
            };
            
            resultDiv.innerHTML = `
                <div class="result">
                    <h4>✅ Dati simulati con successo!</h4>
                    <p><strong>Dati di input:</strong> ${Object.keys(mockInputData).length} parametri</p>
                    <p><strong>Risultati:</strong> ${Object.keys(mockResults).length} sezioni</p>
                    <p><strong>Piani:</strong> ${mockResults.floor_forces.length}</p>
                    <p><strong>Dissipatori:</strong> ${mockResults.damper_recommendations ? 'Inclusi' : 'Non inclusi'}</p>
                    <details>
                        <summary>Visualizza dati simulati</summary>
                        <pre>${JSON.stringify({input: mockInputData, results: mockResults}, null, 2)}</pre>
                    </details>
                </div>
            `;
        }
        
        // Test 3: Generazione Report
        async function generateTestReport() {
            const resultDiv = document.getElementById('reportResult');
            resultDiv.innerHTML = '<p>🔄 Generando report di test...</p>';
            
            try {
                // Assicurati che i dati siano disponibili
                if (!window.inertialMassState) {
                    simulateData();
                }
                
                // Carica il template
                const response = await fetch('/progetti/asdp/inertial_mass/report_template.html');
                if (!response.ok) {
                    throw new Error('Template non disponibile');
                }
                
                let templateHTML = await response.text();
                
                // Simula la funzione di popolamento (versione semplificata)
                const populatedHTML = populateTestTemplate(templateHTML);
                
                // Conta i placeholder sostituiti
                const originalPlaceholders = (templateHTML.match(/{{[^}]+}}/g) || []).length;
                const remainingPlaceholders = (populatedHTML.match(/{{[^}]+}}/g) || []).length;
                const replacedPlaceholders = originalPlaceholders - remainingPlaceholders;
                
                resultDiv.innerHTML = `
                    <div class="result">
                        <h4>✅ Report generato con successo!</h4>
                        <p><strong>Placeholder originali:</strong> ${originalPlaceholders}</p>
                        <p><strong>Placeholder sostituiti:</strong> ${replacedPlaceholders}</p>
                        <p><strong>Placeholder rimanenti:</strong> ${remainingPlaceholders}</p>
                        <p><strong>Dimensione finale:</strong> ${(populatedHTML.length / 1024).toFixed(2)} KB</p>
                        <button class="button" onclick="previewReport()">Anteprima Report</button>
                    </div>
                `;
                
                // Memorizza il report generato per l'anteprima
                window.generatedReport = populatedHTML;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Errore generazione report</h4>
                        <p><strong>Errore:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Test 4: Apertura Finestra
        function testWindowOpening() {
            const resultDiv = document.getElementById('windowResult');
            
            try {
                if (!window.generatedReport) {
                    throw new Error('Genera prima il report con il Test 3');
                }
                
                const reportWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
                
                if (!reportWindow) {
                    throw new Error('Popup bloccato dal browser. Abilita i popup per questo sito.');
                }
                
                reportWindow.document.write(window.generatedReport);
                reportWindow.document.close();
                reportWindow.document.title = 'Report Test - ASDP v2.5.0';
                
                resultDiv.innerHTML = `
                    <div class="result">
                        <h4>✅ Finestra aperta con successo!</h4>
                        <p><strong>Dimensioni:</strong> 1200x800 px</p>
                        <p><strong>Funzionalità:</strong> Scrollbars e ridimensionamento abilitati</p>
                        <p><strong>Contenuto:</strong> Report completo caricato</p>
                        <p><em>Controlla la nuova finestra del browser per vedere il report.</em></p>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Errore apertura finestra</h4>
                        <p><strong>Errore:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Funzione semplificata per popolare il template
        function populateTestTemplate(templateHTML) {
            const now = new Date();
            const timestamp = now.toLocaleDateString('it-IT');
            const fullTimestamp = now.toLocaleString('it-IT');
            
            return templateHTML
                .replace(/{{TIMESTAMP}}/g, timestamp)
                .replace(/{{FULL_TIMESTAMP}}/g, fullTimestamp)
                .replace(/{{CALCULATION_ID}}/g, mockResults.calculation_id)
                .replace(/{{COORDINATES}}/g, `${mockInputData.location.lat}, ${mockInputData.location.lon}`)
                .replace(/{{SEISMIC_ZONE}}/g, mockInputData.seismic_params.zone)
                .replace(/{{AG}}/g, mockInputData.seismic_params.ag)
                .replace(/{{F0}}/g, mockInputData.seismic_params.F0)
                .replace(/{{TC}}/g, mockInputData.seismic_params.TC)
                .replace(/{{SOIL_CATEGORY}}/g, mockInputData.seismic_params.soil_category)
                .replace(/{{CONSTRUCTION_CATEGORY}}/g, 'Edificio Generico')
                .replace(/{{STRUCTURE_TYPE}}/g, 'Cemento Armato')
                .replace(/{{SLAB_TYPE}}/g, 'Laterocemento')
                .replace(/{{CONSTRUCTION_YEAR}}/g, mockInputData.building.construction_year)
                .replace(/{{TOTAL_MASS}}/g, mockResults.total_mass)
                .replace(/{{PERIOD}}/g, mockResults.period)
                .replace(/{{TOTAL_FORCE}}/g, mockResults.total_force)
                .replace(/{{RESPONSE_SPECTRUM}}/g, mockResults.response_spectrum)
                .replace(/{{FLOOR_FORCES_TABLE}}/g, generateMockFloorTable())
                .replace(/{{REQUIRED_DISSIPATION}}/g, mockResults.damper_recommendations.required_dissipation)
                .replace(/{{EFFICIENCY_RATIO}}/g, mockResults.damper_recommendations.optimal_combination.efficiency_ratio)
                .replace(/{{TOTAL_DAMPERS}}/g, mockResults.damper_recommendations.optimal_combination.total_dampers)
                .replace(/{{DAMPERS_TABLE}}/g, generateMockDampersTable())
                .replace(/{{TECHNICAL_EXPLANATION}}/g, mockResults.damper_recommendations.technical_explanation)
                .replace(/{{BUILDING_NOTES}}/g, generateMockBuildingNotes())
                .replace(/{{AI_ANALYSIS_SECTION}}/g, generateMockAISection());
        }
        
        function generateMockFloorTable() {
            return mockResults.floor_forces.map(floor => `
                <tr>
                    <td><strong>${floor.level}</strong></td>
                    <td>${floor.mass}</td>
                    <td>${floor.height}</td>
                    <td>Residenziale</td>
                    <td><strong>${floor.force}</strong></td>
                </tr>
            `).join('');
        }
        
        function generateMockDampersTable() {
            return mockResults.damper_recommendations.optimal_combination.dampers.map(damper => `
                <tr>
                    <td><strong>Categoria ${damper.type}</strong></td>
                    <td>${damper.capacity_each} kN</td>
                    <td><strong>${damper.quantity}x</strong></td>
                    <td><strong>${damper.total_capacity} kN</strong></td>
                </tr>
            `).join('') + `
                <tr style="background: rgba(72, 187, 120, 0.2); border-top: 2px solid #48bb78;">
                    <td colspan="3"><strong>🎯 TOTALE SISTEMA</strong></td>
                    <td><strong style="color: #48bb78;">${mockResults.damper_recommendations.optimal_combination.total_capacity} kN</strong></td>
                </tr>
            `;
        }
        
        function generateMockBuildingNotes() {
            return mockResults.damper_recommendations.building_analysis.map(note => `
                <div class="building-note">
                    <span class="note-icon">💡</span>
                    <span class="note-text">${note}</span>
                </div>
            `).join('');
        }
        
        function generateMockAISection() {
            return `
                <div class="section">
                    <h2><i class="fas fa-robot"></i> Analisi AI Avanzata</h2>
                    <div class="technical-analysis">${mockResults.llm_analysis}</div>
                </div>
            `;
        }
        
        function previewReport() {
            if (window.generatedReport) {
                const previewWindow = window.open('', '_blank', 'width=1000,height=700,scrollbars=yes');
                previewWindow.document.write(window.generatedReport);
                previewWindow.document.close();
                previewWindow.document.title = 'Anteprima Report - ASDP v2.5.0';
            }
        }
    </script>
</body>
</html>
